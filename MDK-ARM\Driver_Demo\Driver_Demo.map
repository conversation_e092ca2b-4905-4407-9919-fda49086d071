Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) for DMA1_Stream0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) for DMA1_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to usart.o(i.MX_UART5_Init) for MX_UART5_Init
    main.o(i.main) refers to usart.o(i.MX_UART4_Init) for MX_UART4_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to scheduler.o(i.Scheduler_Init) for Scheduler_Init
    main.o(i.main) refers to scheduler.o(i.Scheduler_Run) for Scheduler_Run
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for hi2c1
    i2c.o(i.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for hi2c2
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for htim1
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for htim2
    tim.o(i.MX_TIM3_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for htim3
    tim.o(i.MX_TIM4_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for htim4
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for hdma_uart4_rx
    usart.o(i.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART4_Init) refers to usart.o(.bss) for huart4
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART5_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART5_Init) refers to usart.o(.bss) for huart5
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for huart1
    usart.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    usart.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to usart.o(.bss) for hdma_uart5_rx
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_uart4_rx
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to usart.o(.bss) for huart4
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to usart.o(.bss) for huart5
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADD10) for I2C_Master_ADD10
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) for I2C_SlaveTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF) for I2C_SlaveTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) for I2C_SlaveReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_BTF) for I2C_SlaveReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) for I2C_WaitOnSTOPRequestThroughIT
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for flagBitshiftOffset
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to scheduler.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to scheduler.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_driver.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to uart_driver.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    hardware_iic.o(i.IIC_Anolog_Normalize) refers to hardware_iic.o(i.IIC_WriteBytes) for IIC_WriteBytes
    hardware_iic.o(i.IIC_Get_Anolog) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Digtal) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Offset) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Single_Anolog) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_ReadByte) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    hardware_iic.o(i.IIC_ReadByte) refers to i2c.o(.bss) for hi2c1
    hardware_iic.o(i.IIC_ReadBytes) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(i.IIC_ReadBytes) refers to i2c.o(.bss) for hi2c1
    hardware_iic.o(i.IIC_WriteByte) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    hardware_iic.o(i.IIC_WriteByte) refers to i2c.o(.bss) for hi2c1
    hardware_iic.o(i.IIC_WriteBytes) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    hardware_iic.o(i.IIC_WriteBytes) refers to i2c.o(.bss) for hi2c1
    hardware_iic.o(i.Ping) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr4
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to my_hwt101.o(i.Hwt101_RxEventCallback) for Hwt101_RxEventCallback
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to uart_driver.o(.bss) for uart_rx_dma_buffer
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    uart_driver.o(i.Uart_Passthrough_Init) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    uart_driver.o(i.Uart_Passthrough_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_driver.o(i.Uart_Passthrough_Init) refers to uart_driver.o(.bss) for uart5_ring_buffer_input
    uart_driver.o(i.Uart_Passthrough_Init) refers to usart.o(.bss) for huart5
    uart_driver.o(i.Uart_Passthrough_Task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_driver.o(i.Uart_Passthrough_Task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_driver.o(i.Uart_Passthrough_Task) refers to uart_driver.o(i.Uart_Printf) for Uart_Printf
    uart_driver.o(i.Uart_Passthrough_Task) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    uart_driver.o(i.Uart_Passthrough_Task) refers to memseta.o(.text) for __aeabi_memclr4
    uart_driver.o(i.Uart_Passthrough_Task) refers to uart_driver.o(.bss) for uart5_ring_buffer
    uart_driver.o(i.Uart_Passthrough_Task) refers to usart.o(.bss) for huart1
    uart_driver.o(i.Uart_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart_driver.o(i.Uart_Printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    hwt101_driver.o(i.HWT101_ConvertAngleData) refers to dflti.o(.text) for __aeabi_i2d
    hwt101_driver.o(i.HWT101_ConvertAngleData) refers to dmul.o(.text) for __aeabi_dmul
    hwt101_driver.o(i.HWT101_ConvertAngleData) refers to d2f.o(.text) for __aeabi_d2f
    hwt101_driver.o(i.HWT101_Enable) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_GetData) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_GetGyroZ) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_GetState) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_GetYaw) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_ParseAnglePacket) refers to hwt101_driver.o(i.HWT101_ConvertAngleData) for HWT101_ConvertAngleData
    hwt101_driver.o(i.HWT101_ParseAnglePacket) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    hwt101_driver.o(i.HWT101_ParseGyroPacket) refers to hwt101_driver.o(i.HWT101_ConvertGyroData) for HWT101_ConvertGyroData
    hwt101_driver.o(i.HWT101_ParseGyroPacket) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    hwt101_driver.o(i.HWT101_ProcessBuffer) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_ProcessBuffer) refers to hwt101_driver.o(i.HWT101_CalculateChecksum) for HWT101_CalculateChecksum
    hwt101_driver.o(i.HWT101_ProcessBuffer) refers to hwt101_driver.o(i.HWT101_ParseGyroPacket) for HWT101_ParseGyroPacket
    hwt101_driver.o(i.HWT101_ProcessBuffer) refers to hwt101_driver.o(i.HWT101_ParseAnglePacket) for HWT101_ParseAnglePacket
    hwt101_driver.o(i.HWT101_ResetYaw) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_ResetYaw) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_ResetYaw) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_ResetYaw) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_ResetYaw) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_SaveConfig) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_SaveConfig) refers to usart.o(i.my_printf) for my_printf
    hwt101_driver.o(i.HWT101_SaveConfig) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_SendCommand) refers to usart.o(i.my_printf) for my_printf
    hwt101_driver.o(i.HWT101_SendCommand) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_UnlockRegister) refers to usart.o(i.my_printf) for my_printf
    hwt101_driver.o(i.HWT101_UnlockRegister) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    encoder_driver.o(i.Encoder_Driver_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    motor_driver.o(i.Motor_Create) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    motor_driver.o(i.Motor_Create) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(i.Motor_Enable) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_Enable) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(i.Motor_GetState) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_SetSpeed) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_SetSpeed) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(i.Motor_SetSpeed) refers to motor_driver.o(i.Speed_To_PWM) for Speed_To_PWM
    motor_driver.o(i.Motor_Stop) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for initcmd1
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for F8X16
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for Hzk
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for Hzb
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_cmd) refers to i2c.o(.bss) for hi2c2
    oled.o(i.OLED_Write_data) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_data) refers to i2c.o(.bss) for hi2c2
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_formula_incremental) for pid_formula_incremental
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_out_limit) for pid_out_limit
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_formula_positional) for pid_formula_positional
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_out_limit) for pid_out_limit
    scheduler.o(i.Car_State_Upstate) refers to pid.o(i.pid_set_target) for pid_set_target
    scheduler.o(i.Car_State_Upstate) refers to motor_driver.o(i.Motor_Stop) for Motor_Stop
    scheduler.o(i.Car_State_Upstate) refers to pid.o(i.pid_reset) for pid_reset
    scheduler.o(i.Car_State_Upstate) refers to scheduler.o(.data) for ff_count
    scheduler.o(i.Car_State_Upstate) refers to my_pid.o(.data) for Pid_control_mode
    scheduler.o(i.Car_State_Upstate) refers to my_pid.o(.bss) for pid_angle
    scheduler.o(i.Car_State_Upstate) refers to my_motor.o(.bss) for left_motor
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to my_encoder.o(i.Encoder_Task) for Encoder_Task
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to my_gray.o(i.Gray_Task) for Gray_Task
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to tim.o(.bss) for htim2
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to scheduler.o(.data) for task_counter_5ms
    scheduler.o(i.Scheduler_Init) refers to scheduler.o(i.System_Init) for System_Init
    scheduler.o(i.Scheduler_Init) refers to scheduler.o(.data) for task_num
    scheduler.o(i.Scheduler_Run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.Scheduler_Run) refers to scheduler.o(.data) for scheduler_task
    scheduler.o(i.System_Init) refers to oled.o(i.OLED_Init) for OLED_Init
    scheduler.o(i.System_Init) refers to my_usart.o(i.Uart_Init) for Uart_Init
    scheduler.o(i.System_Init) refers to my_motor.o(i.Motor_Init) for Motor_Init
    scheduler.o(i.System_Init) refers to my_encoder.o(i.Encoder_Init) for Encoder_Init
    scheduler.o(i.System_Init) refers to my_hwt101.o(i.Hwt101_Init) for Hwt101_Init
    scheduler.o(i.System_Init) refers to my_pid.o(i.PID_Init) for PID_Init
    scheduler.o(i.System_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    scheduler.o(i.System_Init) refers to tim.o(.bss) for htim2
    scheduler.o(.data) refers to my_hwt101.o(i.Hwt101_Task) for Hwt101_Task
    my_oled.o(i.OLED_SendBuff) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    my_oled.o(i.OLED_SendBuff) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    my_oled.o(i.Oled_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    my_oled.o(i.Oled_Printf) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    my_usart.o(i.Uart_Init) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    my_usart.o(i.Uart_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    my_usart.o(i.Uart_Init) refers to uart_driver.o(i.Uart_Passthrough_Init) for Uart_Passthrough_Init
    my_usart.o(i.Uart_Init) refers to uart_driver.o(.bss) for ring_buffer_input
    my_usart.o(i.Uart_Init) refers to usart.o(.bss) for huart1
    my_usart.o(i.Uart_Task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    my_usart.o(i.Uart_Task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    my_usart.o(i.Uart_Task) refers to uart_driver.o(i.Uart_Printf) for Uart_Printf
    my_usart.o(i.Uart_Task) refers to memseta.o(.text) for __aeabi_memclr
    my_usart.o(i.Uart_Task) refers to uart_driver.o(i.Uart_Passthrough_Task) for Uart_Passthrough_Task
    my_usart.o(i.Uart_Task) refers to uart_driver.o(.bss) for ring_buffer
    my_usart.o(i.Uart_Task) refers to usart.o(.bss) for huart1
    my_motor.o(i.Motor_Init) refers to motor_driver.o(i.Motor_Create) for Motor_Create
    my_motor.o(i.Motor_Init) refers to tim.o(.bss) for htim1
    my_motor.o(i.Motor_Init) refers to my_motor.o(.bss) for right_motor
    my_encoder.o(i.Encoder_Init) refers to encoder_driver.o(i.Encoder_Driver_Init) for Encoder_Driver_Init
    my_encoder.o(i.Encoder_Init) refers to tim.o(.bss) for htim4
    my_encoder.o(i.Encoder_Init) refers to my_encoder.o(.bss) for left_encoder
    my_encoder.o(i.Encoder_Task) refers to encoder_driver.o(i.Encoder_Driver_Update) for Encoder_Driver_Update
    my_encoder.o(i.Encoder_Task) refers to my_encoder.o(.bss) for left_encoder
    my_pid.o(i.Angle_PID_control) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    my_pid.o(i.Angle_PID_control) refers to pid.o(i.pid_constrain) for pid_constrain
    my_pid.o(i.Angle_PID_control) refers to pid.o(i.pid_set_target) for pid_set_target
    my_pid.o(i.Angle_PID_control) refers to my_hwt101.o(.data) for Yaw
    my_pid.o(i.Angle_PID_control) refers to my_pid.o(.bss) for pid_angle
    my_pid.o(i.Angle_PID_control) refers to my_pid.o(.data) for pid_params_angle
    my_pid.o(i.Line_PID_control) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    my_pid.o(i.Line_PID_control) refers to pid.o(i.pid_constrain) for pid_constrain
    my_pid.o(i.Line_PID_control) refers to pid.o(i.pid_set_target) for pid_set_target
    my_pid.o(i.Line_PID_control) refers to my_gray.o(.data) for g_line_position_error
    my_pid.o(i.Line_PID_control) refers to my_pid.o(.bss) for pid_line
    my_pid.o(i.Line_PID_control) refers to my_pid.o(.data) for pid_params_line
    my_pid.o(i.PID_Init) refers to pid.o(i.pid_init) for pid_init
    my_pid.o(i.PID_Init) refers to pid.o(i.pid_set_target) for pid_set_target
    my_pid.o(i.PID_Init) refers to my_pid.o(.data) for pid_params_left
    my_pid.o(i.PID_Init) refers to my_pid.o(.bss) for pid_speed_left
    my_pid.o(i.PID_Task) refers to my_pid.o(i.Line_PID_control) for Line_PID_control
    my_pid.o(i.PID_Task) refers to pid.o(i.pid_calculate_incremental) for pid_calculate_incremental
    my_pid.o(i.PID_Task) refers to pid.o(i.pid_constrain) for pid_constrain
    my_pid.o(i.PID_Task) refers to motor_driver.o(i.Motor_SetSpeed) for Motor_SetSpeed
    my_pid.o(i.PID_Task) refers to my_pid.o(.data) for pid_running
    my_pid.o(i.PID_Task) refers to my_encoder.o(.bss) for left_encoder
    my_pid.o(i.PID_Task) refers to my_pid.o(.bss) for pid_speed_left
    my_pid.o(i.PID_Task) refers to my_motor.o(.bss) for left_motor
    my_gray.o(i.Gray_Task) refers to hardware_iic.o(i.IIC_Get_Digtal) for IIC_Get_Digtal
    my_gray.o(i.Gray_Task) refers to uart_driver.o(i.Uart_Printf) for Uart_Printf
    my_gray.o(i.Gray_Task) refers to my_gray.o(.data) for Digtal
    my_gray.o(i.Gray_Task) refers to usart.o(.bss) for huart1
    my_hwt101.o(i.Hwt101_DebugData) refers to hwt101_driver.o(i.HWT101_GetData) for HWT101_GetData
    my_hwt101.o(i.Hwt101_DebugData) refers to hwt101_driver.o(i.HWT101_GetState) for HWT101_GetState
    my_hwt101.o(i.Hwt101_DebugData) refers to f2d.o(.text) for __aeabi_f2d
    my_hwt101.o(i.Hwt101_DebugData) refers to uart_driver.o(i.Uart_Printf) for Uart_Printf
    my_hwt101.o(i.Hwt101_DebugData) refers to my_hwt101.o(.bss) for hwt101
    my_hwt101.o(i.Hwt101_DebugData) refers to my_hwt101.o(.conststring) for .conststring
    my_hwt101.o(i.Hwt101_DebugData) refers to usart.o(.bss) for huart1
    my_hwt101.o(i.Hwt101_GetCurrentYaw) refers to my_hwt101.o(.data) for Yaw
    my_hwt101.o(i.Hwt101_Init) refers to hwt101_driver.o(i.HWT101_Create) for HWT101_Create
    my_hwt101.o(i.Hwt101_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    my_hwt101.o(i.Hwt101_Init) refers to hwt101_driver.o(i.HWT101_Enable) for HWT101_Enable
    my_hwt101.o(i.Hwt101_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    my_hwt101.o(i.Hwt101_Init) refers to hwt101_driver.o(i.HWT101_SetOutputRate) for HWT101_SetOutputRate
    my_hwt101.o(i.Hwt101_Init) refers to hwt101_driver.o(i.HWT101_ResetYaw) for HWT101_ResetYaw
    my_hwt101.o(i.Hwt101_Init) refers to uart_driver.o(i.Uart_Printf) for Uart_Printf
    my_hwt101.o(i.Hwt101_Init) refers to usart.o(.bss) for huart4
    my_hwt101.o(i.Hwt101_Init) refers to my_hwt101.o(.bss) for hwt101
    my_hwt101.o(i.Hwt101_Reinit) refers to uart_driver.o(i.Uart_Printf) for Uart_Printf
    my_hwt101.o(i.Hwt101_Reinit) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    my_hwt101.o(i.Hwt101_Reinit) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    my_hwt101.o(i.Hwt101_Reinit) refers to memseta.o(.text) for __aeabi_memclr4
    my_hwt101.o(i.Hwt101_Reinit) refers to hwt101_driver.o(i.HWT101_Create) for HWT101_Create
    my_hwt101.o(i.Hwt101_Reinit) refers to hwt101_driver.o(i.HWT101_Enable) for HWT101_Enable
    my_hwt101.o(i.Hwt101_Reinit) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    my_hwt101.o(i.Hwt101_Reinit) refers to hwt101_driver.o(i.HWT101_SetOutputRate) for HWT101_SetOutputRate
    my_hwt101.o(i.Hwt101_Reinit) refers to hwt101_driver.o(i.HWT101_ResetYaw) for HWT101_ResetYaw
    my_hwt101.o(i.Hwt101_Reinit) refers to usart.o(.bss) for huart1
    my_hwt101.o(i.Hwt101_Reinit) refers to my_hwt101.o(.bss) for hwt101_rx_buffer
    my_hwt101.o(i.Hwt101_RxEventCallback) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    my_hwt101.o(i.Hwt101_RxEventCallback) refers to hwt101_driver.o(i.HWT101_ProcessBuffer) for HWT101_ProcessBuffer
    my_hwt101.o(i.Hwt101_RxEventCallback) refers to uart_driver.o(i.Uart_Printf) for Uart_Printf
    my_hwt101.o(i.Hwt101_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr4
    my_hwt101.o(i.Hwt101_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    my_hwt101.o(i.Hwt101_RxEventCallback) refers to my_hwt101.o(.data) for rx_packet_count
    my_hwt101.o(i.Hwt101_RxEventCallback) refers to my_hwt101.o(.bss) for hwt101_rx_buffer
    my_hwt101.o(i.Hwt101_RxEventCallback) refers to usart.o(.bss) for huart1
    my_hwt101.o(i.Hwt101_Task) refers to hwt101_driver.o(i.HWT101_GetData) for HWT101_GetData
    my_hwt101.o(i.Hwt101_Task) refers to hwt101_driver.o(i.HWT101_GetState) for HWT101_GetState
    my_hwt101.o(i.Hwt101_Task) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    my_hwt101.o(i.Hwt101_Task) refers to f2d.o(.text) for __aeabi_f2d
    my_hwt101.o(i.Hwt101_Task) refers to uart_driver.o(i.Uart_Printf) for Uart_Printf
    my_hwt101.o(i.Hwt101_Task) refers to my_hwt101.o(i.Hwt101_Reinit) for Hwt101_Reinit
    my_hwt101.o(i.Hwt101_Task) refers to my_hwt101.o(.bss) for hwt101
    my_hwt101.o(i.Hwt101_Task) refers to my_hwt101.o(.data) for Yaw
    my_hwt101.o(i.Hwt101_Task) refers to usart.o(.bss) for huart1
    my_hwt101.o(i.Hwt101_Task) refers to scheduler.o(.data) for ff_count
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (100 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (68 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (88 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (172 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (60 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (474 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (440 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (126 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (780 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (676 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (548 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (288 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (368 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (232 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (612 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (264 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (540 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (384 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (308 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (152 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (158 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (158 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (308 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (152 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (28 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (248 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (80 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (318 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Flush_DR), (18 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (428 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (260 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead), (312 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite), (192 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (160 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (216 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADD10), (42 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (616 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (158 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (198 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_BTF), (26 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF), (26 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (112 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT), (80 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (68 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (244 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (192 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (56 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (112 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (56 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (148 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (416 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (384 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (48 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (152 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (100 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (260 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (28 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (144 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (112 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (52 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (120 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (48 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (36 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (56 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (172 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (100 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (34 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (148 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (32 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (18 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (124 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (4056 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (112 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (346 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (102 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (180 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (32 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (120 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (92 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (44 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (48 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (48 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (44 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (80 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (48 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (120 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (156 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (44 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (240 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (156 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (248 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (50 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (278 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (476 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (476 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (556 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (252 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (214 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (280 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (256 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (54 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (222 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (316 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (608 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (400 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (138 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (222 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (588 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (352 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (168 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (276 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (252 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (310 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (164 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (184 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (588 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (352 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (168 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (276 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (252 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (50 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (122 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (104 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (178 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig), (60 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (164 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (38 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (216 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (280 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (228 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (78 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (84 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (90 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (504 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (324 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (136 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (230 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (138 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (162 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (504 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (324 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (136 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (230 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (42 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (20 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (20 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (82 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (68 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (122 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (80 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (82 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (82 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (158 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (296 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (282 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (196 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (220 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (126 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (336 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (166 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (170 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (194 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (156 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (88 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (24 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (64 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (180 bytes).
    Removing hardware_iic.o(.rev16_text), (4 bytes).
    Removing hardware_iic.o(.revsh_text), (4 bytes).
    Removing hardware_iic.o(.rrx_text), (6 bytes).
    Removing hardware_iic.o(i.IIC_Anolog_Normalize), (16 bytes).
    Removing hardware_iic.o(i.IIC_Get_Anolog), (28 bytes).
    Removing hardware_iic.o(i.IIC_Get_Offset), (32 bytes).
    Removing hardware_iic.o(i.IIC_Get_Single_Anolog), (26 bytes).
    Removing hardware_iic.o(i.IIC_ReadByte), (32 bytes).
    Removing hardware_iic.o(i.IIC_WriteByte), (48 bytes).
    Removing hardware_iic.o(i.IIC_WriteBytes), (48 bytes).
    Removing hardware_iic.o(i.Ping), (30 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_get), (164 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (88 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (102 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (232 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (90 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (126 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (36 bytes).
    Removing uart_driver.o(.rev16_text), (4 bytes).
    Removing uart_driver.o(.revsh_text), (4 bytes).
    Removing uart_driver.o(.rrx_text), (6 bytes).
    Removing uart_driver.o(i.Uart_Passthrough_Task), (108 bytes).
    Removing hwt101_driver.o(.rev16_text), (4 bytes).
    Removing hwt101_driver.o(.revsh_text), (4 bytes).
    Removing hwt101_driver.o(.rrx_text), (6 bytes).
    Removing hwt101_driver.o(i.HWT101_GetGyroZ), (52 bytes).
    Removing hwt101_driver.o(i.HWT101_GetYaw), (52 bytes).
    Removing hwt101_driver.o(i.HWT101_SetBaudRate), (102 bytes).
    Removing hwt101_driver.o(i.HWT101_StartManualCalibration), (88 bytes).
    Removing hwt101_driver.o(i.HWT101_StopManualCalibration), (88 bytes).
    Removing encoder_driver.o(.rev16_text), (4 bytes).
    Removing encoder_driver.o(.revsh_text), (4 bytes).
    Removing encoder_driver.o(.rrx_text), (6 bytes).
    Removing motor_driver.o(.rev16_text), (4 bytes).
    Removing motor_driver.o(.revsh_text), (4 bytes).
    Removing motor_driver.o(.rrx_text), (6 bytes).
    Removing motor_driver.o(i.Motor_Enable), (98 bytes).
    Removing motor_driver.o(i.Motor_GetState), (20 bytes).
    Removing motor_driver.o(i.Motor_SetSpeed), (252 bytes).
    Removing motor_driver.o(i.Motor_Stop), (92 bytes).
    Removing motor_driver.o(i.Motor_ValidateParams), (22 bytes).
    Removing motor_driver.o(i.Speed_To_PWM), (54 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Allfill), (56 bytes).
    Removing oled.o(i.OLED_Display_Off), (22 bytes).
    Removing oled.o(i.OLED_Display_On), (22 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_ShowChar), (156 bytes).
    Removing oled.o(i.OLED_ShowFloat), (352 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (100 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (184 bytes).
    Removing oled.o(i.OLED_ShowNum), (136 bytes).
    Removing oled.o(i.OLED_ShowPic), (76 bytes).
    Removing oled.o(i.OLED_ShowStr), (58 bytes).
    Removing oled.o(.constdata), (2712 bytes).
    Removing pid.o(i.pid_app_limit_integral), (40 bytes).
    Removing pid.o(i.pid_calculate_incremental), (38 bytes).
    Removing pid.o(i.pid_calculate_positional), (38 bytes).
    Removing pid.o(i.pid_constrain), (42 bytes).
    Removing pid.o(i.pid_formula_incremental), (142 bytes).
    Removing pid.o(i.pid_formula_positional), (122 bytes).
    Removing pid.o(i.pid_out_limit), (64 bytes).
    Removing pid.o(i.pid_reset), (64 bytes).
    Removing pid.o(i.pid_set_limit), (6 bytes).
    Removing pid.o(i.pid_set_params), (14 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing scheduler.o(i.Car_State_Upstate), (132 bytes).
    Removing my_oled.o(.rev16_text), (4 bytes).
    Removing my_oled.o(.revsh_text), (4 bytes).
    Removing my_oled.o(.rrx_text), (6 bytes).
    Removing my_oled.o(i.OLED_SendBuff), (62 bytes).
    Removing my_oled.o(i.Oled_Printf), (60 bytes).
    Removing my_oled.o(i.oled_task), (2 bytes).
    Removing my_oled.o(.data), (4 bytes).
    Removing my_usart.o(.rev16_text), (4 bytes).
    Removing my_usart.o(.revsh_text), (4 bytes).
    Removing my_usart.o(.rrx_text), (6 bytes).
    Removing my_usart.o(i.Uart_Task), (84 bytes).
    Removing my_motor.o(.rev16_text), (4 bytes).
    Removing my_motor.o(.revsh_text), (4 bytes).
    Removing my_motor.o(.rrx_text), (6 bytes).
    Removing my_encoder.o(.rev16_text), (4 bytes).
    Removing my_encoder.o(.revsh_text), (4 bytes).
    Removing my_encoder.o(.rrx_text), (6 bytes).
    Removing my_pid.o(.rev16_text), (4 bytes).
    Removing my_pid.o(.revsh_text), (4 bytes).
    Removing my_pid.o(.rrx_text), (6 bytes).
    Removing my_pid.o(i.Angle_PID_control), (136 bytes).
    Removing my_pid.o(i.Line_PID_control), (136 bytes).
    Removing my_pid.o(i.PID_Task), (200 bytes).
    Removing my_gray.o(.rev16_text), (4 bytes).
    Removing my_gray.o(.revsh_text), (4 bytes).
    Removing my_gray.o(.rrx_text), (6 bytes).
    Removing my_gray.o(i.Gray_Init), (2 bytes).
    Removing my_hwt101.o(.rev16_text), (4 bytes).
    Removing my_hwt101.o(.revsh_text), (4 bytes).
    Removing my_hwt101.o(.rrx_text), (6 bytes).
    Removing my_hwt101.o(i.Hwt101_DebugData), (184 bytes).
    Removing my_hwt101.o(i.Hwt101_GetCurrentYaw), (12 bytes).
    Removing my_hwt101.o(.conststring), (69 bytes).

573 unused section(s) (total 59221 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\Components\Encoder\encoder_driver.c   0x00000000   Number         0  encoder_driver.o ABSOLUTE
    ..\Components\Grayscale\hardware_iic.c   0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\Components\Hwt101\hwt101_driver.c     0x00000000   Number         0  hwt101_driver.o ABSOLUTE
    ..\Components\Motor\motor_driver.c       0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\Components\OLED\oled.c                0x00000000   Number         0  oled.o ABSOLUTE
    ..\Components\PID\pid.c                  0x00000000   Number         0  pid.o ABSOLUTE
    ..\Components\ringbuffer\ringbuffer.c    0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\Components\ringbuffer\uart_driver.c   0x00000000   Number         0  uart_driver.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\My_app\My_encoder.c                   0x00000000   Number         0  my_encoder.o ABSOLUTE
    ..\My_app\My_gray.c                      0x00000000   Number         0  my_gray.o ABSOLUTE
    ..\My_app\My_hwt101.c                    0x00000000   Number         0  my_hwt101.o ABSOLUTE
    ..\My_app\My_motor.c                     0x00000000   Number         0  my_motor.o ABSOLUTE
    ..\My_app\My_oled.c                      0x00000000   Number         0  my_oled.o ABSOLUTE
    ..\My_app\My_pid.c                       0x00000000   Number         0  my_pid.o ABSOLUTE
    ..\My_app\My_usart.c                     0x00000000   Number         0  my_usart.o ABSOLUTE
    ..\My_app\scheduler.c                    0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\Components\\Encoder\\encoder_driver.c 0x00000000   Number         0  encoder_driver.o ABSOLUTE
    ..\\Components\\Grayscale\\hardware_iic.c 0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\\Components\\Hwt101\\hwt101_driver.c  0x00000000   Number         0  hwt101_driver.o ABSOLUTE
    ..\\Components\\Motor\\motor_driver.c    0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\\Components\\OLED\\oled.c             0x00000000   Number         0  oled.o ABSOLUTE
    ..\\Components\\ringbuffer\\uart_driver.c 0x00000000   Number         0  uart_driver.o ABSOLUTE
    ..\\My_app\\My_encoder.c                 0x00000000   Number         0  my_encoder.o ABSOLUTE
    ..\\My_app\\My_gray.c                    0x00000000   Number         0  my_gray.o ABSOLUTE
    ..\\My_app\\My_hwt101.c                  0x00000000   Number         0  my_hwt101.o ABSOLUTE
    ..\\My_app\\My_motor.c                   0x00000000   Number         0  my_motor.o ABSOLUTE
    ..\\My_app\\My_oled.c                    0x00000000   Number         0  my_oled.o ABSOLUTE
    ..\\My_app\\My_pid.c                     0x00000000   Number         0  my_pid.o ABSOLUTE
    ..\\My_app\\My_usart.c                   0x00000000   Number         0  my_usart.o ABSOLUTE
    ..\\My_app\\scheduler.c                  0x00000000   Number         0  scheduler.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000198   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000198   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000198   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000198   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x0800019c   Section       36  startup_stm32f407xx.o(.text)
    $v0                                      0x0800019c   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080001c0   Section        0  uldiv.o(.text)
    .text                                    0x08000222   Section        0  memcpya.o(.text)
    .text                                    0x08000246   Section        0  memseta.o(.text)
    .text                                    0x0800026a   Section        0  dmul.o(.text)
    .text                                    0x0800034e   Section        0  dflti.o(.text)
    .text                                    0x08000370   Section        0  f2d.o(.text)
    .text                                    0x08000396   Section        0  d2f.o(.text)
    .text                                    0x080003ce   Section        0  uidiv.o(.text)
    .text                                    0x080003fa   Section        0  llshl.o(.text)
    .text                                    0x08000418   Section        0  llushr.o(.text)
    .text                                    0x08000438   Section        0  iusefp.o(.text)
    .text                                    0x08000438   Section        0  fepilogue.o(.text)
    .text                                    0x080004a6   Section        0  depilogue.o(.text)
    .text                                    0x08000560   Section        0  dadd.o(.text)
    .text                                    0x080006ae   Section        0  ddiv.o(.text)
    .text                                    0x0800078c   Section        0  dfixul.o(.text)
    .text                                    0x080007bc   Section       48  cdrcmple.o(.text)
    .text                                    0x080007ec   Section       36  init.o(.text)
    .text                                    0x08000810   Section        0  llsshr.o(.text)
    i.BusFault_Handler                       0x08000834   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Stream0_IRQHandler                0x08000838   Section        0  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    i.DMA1_Stream2_IRQHandler                0x08000848   Section        0  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x08000858   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08000868   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08000869   Thumb Code    46  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x0800089c   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x0800089d   Thumb Code   170  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08000946   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08000947   Thumb Code    44  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08000972   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Encoder_Driver_Init                    0x08000974   Section        0  encoder_driver.o(i.Encoder_Driver_Init)
    i.Encoder_Driver_Update                  0x080009a4   Section        0  encoder_driver.o(i.Encoder_Driver_Update)
    i.Encoder_Init                           0x08000a0c   Section        0  my_encoder.o(i.Encoder_Init)
    i.Encoder_Task                           0x08000a34   Section        0  my_encoder.o(i.Encoder_Task)
    i.Error_Handler                          0x08000a4c   Section        0  main.o(i.Error_Handler)
    i.Gray_Task                              0x08000a54   Section        0  my_gray.o(i.Gray_Task)
    i.HAL_DMA_Abort                          0x08000b5c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08000c08   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08000c30   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08000e70   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08000f5c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08000ff0   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08001018   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x0800120c   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08001218   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x08001224   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Mem_Read                       0x080013f4   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    i.HAL_I2C_Mem_Write                      0x080016fc   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x08001860   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08001954   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x0800196c   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080019a8   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080019f4   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08001a3c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08001a64   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08001ae0   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08001b08   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08001c8c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08001c98   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08001cb8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08001cd8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001d88   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08002224   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x08002258   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x0800225a   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x0800225c   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x080022d0   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08002384   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x080023ec   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08002460   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x08002508   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x08002614   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x080026dc   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x080027d4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_IC_CaptureCallback             0x080028a0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x080028a2   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08002a10   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08002a74   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x08002a76   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08002b7a   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08002be0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08002be2   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08002be4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x08002cf0   Section        0  scheduler.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08002d30   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08002d32   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08002da4   Section        0  uart_driver.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08002e68   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x08002ef2   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08002ef4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080031fc   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08003274   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x08003518   Section        0  uart_driver.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x0800351a   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x0800351c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x080035da   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HWT101_CalculateChecksum               0x080035dc   Section        0  hwt101_driver.o(i.HWT101_CalculateChecksum)
    HWT101_CalculateChecksum                 0x080035dd   Thumb Code    28  hwt101_driver.o(i.HWT101_CalculateChecksum)
    i.HWT101_ConvertAngleData                0x080035f8   Section        0  hwt101_driver.o(i.HWT101_ConvertAngleData)
    HWT101_ConvertAngleData                  0x080035f9   Thumb Code    88  hwt101_driver.o(i.HWT101_ConvertAngleData)
    i.HWT101_ConvertGyroData                 0x08003658   Section        0  hwt101_driver.o(i.HWT101_ConvertGyroData)
    HWT101_ConvertGyroData                   0x08003659   Thumb Code    36  hwt101_driver.o(i.HWT101_ConvertGyroData)
    i.HWT101_Create                          0x08003684   Section        0  hwt101_driver.o(i.HWT101_Create)
    i.HWT101_Enable                          0x080036ec   Section        0  hwt101_driver.o(i.HWT101_Enable)
    i.HWT101_GetData                         0x08003738   Section        0  hwt101_driver.o(i.HWT101_GetData)
    i.HWT101_GetState                        0x08003760   Section        0  hwt101_driver.o(i.HWT101_GetState)
    i.HWT101_ParseAnglePacket                0x08003776   Section        0  hwt101_driver.o(i.HWT101_ParseAnglePacket)
    HWT101_ParseAnglePacket                  0x08003777   Thumb Code    42  hwt101_driver.o(i.HWT101_ParseAnglePacket)
    i.HWT101_ParseGyroPacket                 0x080037a0   Section        0  hwt101_driver.o(i.HWT101_ParseGyroPacket)
    HWT101_ParseGyroPacket                   0x080037a1   Thumb Code    44  hwt101_driver.o(i.HWT101_ParseGyroPacket)
    i.HWT101_ProcessBuffer                   0x080037cc   Section        0  hwt101_driver.o(i.HWT101_ProcessBuffer)
    i.HWT101_ResetYaw                        0x080038d6   Section        0  hwt101_driver.o(i.HWT101_ResetYaw)
    i.HWT101_SaveConfig                      0x08003930   Section        0  hwt101_driver.o(i.HWT101_SaveConfig)
    i.HWT101_SendCommand                     0x08003968   Section        0  hwt101_driver.o(i.HWT101_SendCommand)
    HWT101_SendCommand                       0x08003969   Thumb Code    52  hwt101_driver.o(i.HWT101_SendCommand)
    i.HWT101_SetOutputRate                   0x080039a8   Section        0  hwt101_driver.o(i.HWT101_SetOutputRate)
    i.HWT101_UnlockRegister                  0x08003a14   Section        0  hwt101_driver.o(i.HWT101_UnlockRegister)
    HWT101_UnlockRegister                    0x08003a15   Thumb Code    30  hwt101_driver.o(i.HWT101_UnlockRegister)
    i.HWT101_ValidateParams                  0x08003a3c   Section        0  hwt101_driver.o(i.HWT101_ValidateParams)
    HWT101_ValidateParams                    0x08003a3d   Thumb Code    18  hwt101_driver.o(i.HWT101_ValidateParams)
    i.HardFault_Handler                      0x08003a4e   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.Hwt101_Init                            0x08003a54   Section        0  my_hwt101.o(i.Hwt101_Init)
    i.Hwt101_Reinit                          0x08003ae8   Section        0  my_hwt101.o(i.Hwt101_Reinit)
    Hwt101_Reinit                            0x08003ae9   Thumb Code   114  my_hwt101.o(i.Hwt101_Reinit)
    i.Hwt101_RxEventCallback                 0x08003bac   Section        0  my_hwt101.o(i.Hwt101_RxEventCallback)
    i.Hwt101_Task                            0x08003c94   Section        0  my_hwt101.o(i.Hwt101_Task)
    i.I2C_IsAcknowledgeFailed                0x08003e78   Section        0  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x08003e79   Thumb Code    62  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_RequestMemoryRead                  0x08003eb8   Section        0  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    I2C_RequestMemoryRead                    0x08003eb9   Thumb Code   348  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    i.I2C_RequestMemoryWrite                 0x08004018   Section        0  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x08004019   Thumb Code   220  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x080040f8   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x080040f9   Thumb Code   102  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x0800415e   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x0800415f   Thumb Code   190  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x0800421c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x0800421d   Thumb Code   250  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnRXNEFlagUntilTimeout         0x08004316   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    I2C_WaitOnRXNEFlagUntilTimeout           0x08004317   Thumb Code   138  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x080043a0   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x080043a1   Thumb Code   102  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.IIC_Get_Digtal                         0x08004406   Section        0  hardware_iic.o(i.IIC_Get_Digtal)
    i.IIC_ReadBytes                          0x0800441c   Section        0  hardware_iic.o(i.IIC_ReadBytes)
    i.MX_DMA_Init                            0x0800444c   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x080044c4   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x080045b8   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C2_Init                           0x080045f4   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_TIM1_Init                           0x08004630   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08004714   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x08004780   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x08004800   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_UART4_Init                          0x0800487c   Section        0  usart.o(i.MX_UART4_Init)
    i.MX_UART5_Init                          0x080048b4   Section        0  usart.o(i.MX_UART5_Init)
    i.MX_USART1_UART_Init                    0x080048ec   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x08004924   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.Motor_Create                           0x08004928   Section        0  motor_driver.o(i.Motor_Create)
    i.Motor_Init                             0x080049c0   Section        0  my_motor.o(i.Motor_Init)
    i.NMI_Handler                            0x08004a00   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08004a04   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x08004a3c   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Position                      0x08004a6c   Section        0  oled.o(i.OLED_Set_Position)
    i.OLED_Write_cmd                         0x08004a90   Section        0  oled.o(i.OLED_Write_cmd)
    i.OLED_Write_data                        0x08004ab8   Section        0  oled.o(i.OLED_Write_data)
    i.PID_Init                               0x08004ae0   Section        0  my_pid.o(i.PID_Init)
    i.PendSV_Handler                         0x08004bf0   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08004bf2   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Scheduler_Init                         0x08004bf4   Section        0  scheduler.o(i.Scheduler_Init)
    i.Scheduler_Run                          0x08004c08   Section        0  scheduler.o(i.Scheduler_Run)
    i.SysTick_Handler                        0x08004c60   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08004c68   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08004d1c   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.System_Init                            0x08004d30   Section        0  scheduler.o(i.System_Init)
    i.TIM2_IRQHandler                        0x08004d58   Section        0  stm32f4xx_it.o(i.TIM2_IRQHandler)
    i.TIM_Base_SetConfig                     0x08004d68   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08004e48   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_ETR_SetConfig                      0x08004e6a   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08004e80   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08004e81   Thumb Code    18  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x08004e94   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08004e95   Thumb Code   104  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08004f04   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08004f80   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08004f81   Thumb Code   112  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08004ff8   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08004ff9   Thumb Code    74  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x0800504c   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x0800504d   Thumb Code    38  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08005072   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08005073   Thumb Code    40  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART4_IRQHandler                       0x0800509c   Section        0  stm32f4xx_it.o(i.UART4_IRQHandler)
    i.UART5_IRQHandler                       0x080050ac   Section        0  stm32f4xx_it.o(i.UART5_IRQHandler)
    i.UART_DMAAbortOnError                   0x080050bc   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080050bd   Thumb Code    18  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x080050ce   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x080050cf   Thumb Code    80  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x0800511e   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x0800511f   Thumb Code   180  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x080051d2   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x080051d3   Thumb Code    36  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x080051f6   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x080051f7   Thumb Code   108  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08005262   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08005263   Thumb Code    32  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x08005282   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08005283   Thumb Code    38  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x080052a8   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x080052a9   Thumb Code   252  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x080053a4   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080053a5   Thumb Code   546  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x080055d0   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Transmit_IT                       0x080056a8   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x080056a9   Thumb Code    96  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08005708   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08005709   Thumb Code   140  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08005794   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.Uart_Init                              0x080057a4   Section        0  my_usart.o(i.Uart_Init)
    i.Uart_Passthrough_Init                  0x080057e8   Section        0  uart_driver.o(i.Uart_Passthrough_Init)
    i.Uart_Printf                            0x08005828   Section        0  uart_driver.o(i.Uart_Printf)
    i.UsageFault_Handler                     0x08005862   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0vsnprintf                           0x08005868   Section        0  printfa.o(i.__0vsnprintf)
    i.__NVIC_GetPriorityGrouping             0x08005894   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    __NVIC_GetPriorityGrouping               0x08005895   Thumb Code    10  stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    i.__NVIC_SetPriority                     0x080058a4   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080058a5   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x080058cc   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080058da   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080058dc   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x080058ec   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080058ed   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08005a70   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08005a71   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x0800614c   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x0800614d   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08006170   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08006171   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x0800619e   Section        0  printfa.o(i._snputc)
    _snputc                                  0x0800619f   Thumb Code    22  printfa.o(i._snputc)
    i.main                                   0x080061b4   Section        0  main.o(i.main)
    i.my_printf                              0x080061f4   Section        0  usart.o(i.my_printf)
    i.pid_init                               0x08006230   Section        0  pid.o(i.pid_init)
    i.pid_set_target                         0x08006284   Section        0  pid.o(i.pid_set_target)
    i.rt_ringbuffer_data_len                 0x0800628a   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_init                     0x080062c6   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x080062f6   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x0800639e   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    .constdata                               0x080063c8   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x080063c8   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x080063d0   Section       24  system_stm32f4xx.o(.constdata)
    .data                                    0x20000000   Section        9  stm32f4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000010   Section       22  oled.o(.data)
    .data                                    0x20000028   Section       36  scheduler.o(.data)
    scheduler_task                           0x2000002c   Data          12  scheduler.o(.data)
    task_counter_5ms                         0x20000048   Data           4  scheduler.o(.data)
    .data                                    0x2000004c   Section       89  my_pid.o(.data)
    .data                                    0x200000a8   Section       40  my_gray.o(.data)
    .data                                    0x200000d0   Section       20  my_hwt101.o(.data)
    error_count                              0x200000d4   Data           4  my_hwt101.o(.data)
    last_valid_time                          0x200000d8   Data           4  my_hwt101.o(.data)
    rx_packet_count                          0x200000dc   Data           4  my_hwt101.o(.data)
    last_rx_time                             0x200000e0   Data           4  my_hwt101.o(.data)
    .bss                                     0x200000e4   Section      168  i2c.o(.bss)
    .bss                                     0x2000018c   Section      288  tim.o(.bss)
    .bss                                     0x200002ac   Section      504  usart.o(.bss)
    .bss                                     0x200004a4   Section     6168  uart_driver.o(.bss)
    .bss                                     0x20001cbc   Section       40  my_motor.o(.bss)
    .bss                                     0x20001ce4   Section       32  my_encoder.o(.bss)
    .bss                                     0x20001d04   Section      240  my_pid.o(.bss)
    .bss                                     0x20001df4   Section      100  my_hwt101.o(.bss)
    STACK                                    0x20001e58   Section     1024  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000199   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000199   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x0800019d   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c1   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x08000223   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000223   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000223   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000247   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000247   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000247   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000255   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000255   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000255   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000259   Thumb Code    18  memseta.o(.text)
    __aeabi_dmul                             0x0800026b   Thumb Code   228  dmul.o(.text)
    __aeabi_i2d                              0x0800034f   Thumb Code    34  dflti.o(.text)
    __aeabi_f2d                              0x08000371   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x08000397   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x080003cf   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080003cf   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080003fb   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080003fb   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000419   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000419   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x08000439   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000439   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x0800044b   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x080004a7   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080004c5   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x08000561   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080006a3   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080006a9   Thumb Code     6  dadd.o(.text)
    __aeabi_ddiv                             0x080006af   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x0800078d   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x080007bd   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x080007ed   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080007ed   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x08000811   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000811   Thumb Code     0  llsshr.o(.text)
    BusFault_Handler                         0x08000835   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DMA1_Stream0_IRQHandler                  0x08000839   Thumb Code    10  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    DMA1_Stream2_IRQHandler                  0x08000849   Thumb Code    10  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08000859   Thumb Code    10  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x08000973   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Encoder_Driver_Init                      0x08000975   Thumb Code    42  encoder_driver.o(i.Encoder_Driver_Init)
    Encoder_Driver_Update                    0x080009a5   Thumb Code    90  encoder_driver.o(i.Encoder_Driver_Update)
    Encoder_Init                             0x08000a0d   Thumb Code    24  my_encoder.o(i.Encoder_Init)
    Encoder_Task                             0x08000a35   Thumb Code    16  my_encoder.o(i.Encoder_Task)
    Error_Handler                            0x08000a4d   Thumb Code     6  main.o(i.Error_Handler)
    Gray_Task                                0x08000a55   Thumb Code   204  my_gray.o(i.Gray_Task)
    HAL_DMA_Abort                            0x08000b5d   Thumb Code   172  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08000c09   Thumb Code    40  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08000c31   Thumb Code   570  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08000e71   Thumb Code   232  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08000f5d   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08000ff1   Thumb Code    36  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08001019   Thumb Code   454  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x0800120d   Thumb Code    12  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001219   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x08001225   Thumb Code   446  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Mem_Read                         0x080013f5   Thumb Code   762  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    HAL_I2C_Mem_Write                        0x080016fd   Thumb Code   348  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x08001861   Thumb Code   228  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08001955   Thumb Code    16  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x0800196d   Thumb Code    54  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080019a9   Thumb Code    64  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080019f5   Thumb Code    68  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001a3d   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001a65   Thumb Code   124  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001ae1   Thumb Code    32  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001b09   Thumb Code   368  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08001c8d   Thumb Code     6  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08001c99   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001cb9   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08001cd9   Thumb Code   162  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001d89   Thumb Code  1172  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08002225   Thumb Code    52  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08002259   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x0800225b   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x0800225d   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x080022d1   Thumb Code   150  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08002385   Thumb Code   102  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x080023ed   Thumb Code   106  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08002461   Thumb Code   138  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x08002509   Thumb Code   268  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x08002615   Thumb Code   200  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x080026dd   Thumb Code   226  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x080027d5   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x080028a1   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x080028a3   Thumb Code   364  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08002a11   Thumb Code    86  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x08002a75   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x08002a77   Thumb Code   260  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08002b7b   Thumb Code   102  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08002be1   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08002be3   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08002be5   Thumb Code   238  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x08002cf1   Thumb Code    54  scheduler.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08002d31   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08002d33   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08002da5   Thumb Code   152  uart_driver.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08002e69   Thumb Code   138  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08002ef3   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08002ef5   Thumb Code   772  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080031fd   Thumb Code   118  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08003275   Thumb Code   626  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08003519   Thumb Code     2  uart_driver.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x0800351b   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x0800351d   Thumb Code   190  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x080035db   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HWT101_Create                            0x08003685   Thumb Code    98  hwt101_driver.o(i.HWT101_Create)
    HWT101_Enable                            0x080036ed   Thumb Code    72  hwt101_driver.o(i.HWT101_Enable)
    HWT101_GetData                           0x08003739   Thumb Code    40  hwt101_driver.o(i.HWT101_GetData)
    HWT101_GetState                          0x08003761   Thumb Code    22  hwt101_driver.o(i.HWT101_GetState)
    HWT101_ProcessBuffer                     0x080037cd   Thumb Code   266  hwt101_driver.o(i.HWT101_ProcessBuffer)
    HWT101_ResetYaw                          0x080038d7   Thumb Code    88  hwt101_driver.o(i.HWT101_ResetYaw)
    HWT101_SaveConfig                        0x08003931   Thumb Code    48  hwt101_driver.o(i.HWT101_SaveConfig)
    HWT101_SetOutputRate                     0x080039a9   Thumb Code   106  hwt101_driver.o(i.HWT101_SetOutputRate)
    HardFault_Handler                        0x08003a4f   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    Hwt101_Init                              0x08003a55   Thumb Code    88  my_hwt101.o(i.Hwt101_Init)
    Hwt101_RxEventCallback                   0x08003bad   Thumb Code   114  my_hwt101.o(i.Hwt101_RxEventCallback)
    Hwt101_Task                              0x08003c95   Thumb Code   224  my_hwt101.o(i.Hwt101_Task)
    IIC_Get_Digtal                           0x08004407   Thumb Code    20  hardware_iic.o(i.IIC_Get_Digtal)
    IIC_ReadBytes                            0x0800441d   Thumb Code    42  hardware_iic.o(i.IIC_ReadBytes)
    MX_DMA_Init                              0x0800444d   Thumb Code   114  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x080044c5   Thumb Code   234  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x080045b9   Thumb Code    48  i2c.o(i.MX_I2C1_Init)
    MX_I2C2_Init                             0x080045f5   Thumb Code    48  i2c.o(i.MX_I2C2_Init)
    MX_TIM1_Init                             0x08004631   Thumb Code   220  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08004715   Thumb Code   102  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x08004781   Thumb Code   120  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x08004801   Thumb Code   116  tim.o(i.MX_TIM4_Init)
    MX_UART4_Init                            0x0800487d   Thumb Code    46  usart.o(i.MX_UART4_Init)
    MX_UART5_Init                            0x080048b5   Thumb Code    46  usart.o(i.MX_UART5_Init)
    MX_USART1_UART_Init                      0x080048ed   Thumb Code    46  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x08004925   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    Motor_Create                             0x08004929   Thumb Code   150  motor_driver.o(i.Motor_Create)
    Motor_Init                               0x080049c1   Thumb Code    46  my_motor.o(i.Motor_Init)
    NMI_Handler                              0x08004a01   Thumb Code     4  stm32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08004a05   Thumb Code    56  oled.o(i.OLED_Clear)
    OLED_Init                                0x08004a3d   Thumb Code    42  oled.o(i.OLED_Init)
    OLED_Set_Position                        0x08004a6d   Thumb Code    36  oled.o(i.OLED_Set_Position)
    OLED_Write_cmd                           0x08004a91   Thumb Code    34  oled.o(i.OLED_Write_cmd)
    OLED_Write_data                          0x08004ab9   Thumb Code    34  oled.o(i.OLED_Write_data)
    PID_Init                                 0x08004ae1   Thumb Code   232  my_pid.o(i.PID_Init)
    PendSV_Handler                           0x08004bf1   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08004bf3   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Scheduler_Init                           0x08004bf5   Thumb Code    14  scheduler.o(i.Scheduler_Init)
    Scheduler_Run                            0x08004c09   Thumb Code    78  scheduler.o(i.Scheduler_Run)
    SysTick_Handler                          0x08004c61   Thumb Code     8  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08004c69   Thumb Code   170  main.o(i.SystemClock_Config)
    SystemInit                               0x08004d1d   Thumb Code    14  system_stm32f4xx.o(i.SystemInit)
    System_Init                              0x08004d31   Thumb Code    34  scheduler.o(i.System_Init)
    TIM2_IRQHandler                          0x08004d59   Thumb Code    10  stm32f4xx_it.o(i.TIM2_IRQHandler)
    TIM_Base_SetConfig                       0x08004d69   Thumb Code   178  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08004e49   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x08004e6b   Thumb Code    22  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08004f05   Thumb Code   114  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART4_IRQHandler                         0x0800509d   Thumb Code    10  stm32f4xx_it.o(i.UART4_IRQHandler)
    UART5_IRQHandler                         0x080050ad   Thumb Code    10  stm32f4xx_it.o(i.UART5_IRQHandler)
    UART_Start_Receive_DMA                   0x080055d1   Thumb Code   202  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    USART1_IRQHandler                        0x08005795   Thumb Code    10  stm32f4xx_it.o(i.USART1_IRQHandler)
    Uart_Init                                0x080057a5   Thumb Code    48  my_usart.o(i.Uart_Init)
    Uart_Passthrough_Init                    0x080057e9   Thumb Code    44  uart_driver.o(i.Uart_Passthrough_Init)
    Uart_Printf                              0x08005829   Thumb Code    58  uart_driver.o(i.Uart_Printf)
    UsageFault_Handler                       0x08005863   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    __0vsnprintf                             0x08005869   Thumb Code    40  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08005869   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08005869   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08005869   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08005869   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __scatterload_copy                       0x080058cd   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080058db   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080058dd   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    main                                     0x080061b5   Thumb Code    64  main.o(i.main)
    my_printf                                0x080061f5   Thumb Code    58  usart.o(i.my_printf)
    pid_init                                 0x08006231   Thumb Code    78  pid.o(i.pid_init)
    pid_set_target                           0x08006285   Thumb Code     6  pid.o(i.pid_set_target)
    rt_ringbuffer_data_len                   0x0800628b   Thumb Code    60  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_init                       0x080062c7   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x080062f7   Thumb Code   168  ringbuffer.o(i.rt_ringbuffer_put)
    rt_ringbuffer_status                     0x0800639f   Thumb Code    42  ringbuffer.o(i.rt_ringbuffer_status)
    AHBPrescTable                            0x080063d0   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x080063e0   Data           8  system_stm32f4xx.o(.constdata)
    Region$$Table$$Base                      0x080063e8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006408   Number         0  anon$$obj.o(Region$$Table)
    uwTick                                   0x20000000   Data           4  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTickFreq                               0x20000008   Data           1  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    initcmd1                                 0x20000010   Data          22  oled.o(.data)
    task_num                                 0x20000028   Data           1  scheduler.o(.data)
    Car_State                                0x20000038   Data           1  scheduler.o(.data)
    Last_Car_Statr                           0x20000039   Data           1  scheduler.o(.data)
    Line_State_Time                          0x2000003c   Data           4  scheduler.o(.data)
    White_State_Time                         0x20000040   Data           4  scheduler.o(.data)
    ff_count                                 0x20000044   Data           4  scheduler.o(.data)
    Basic_Speed                              0x2000004c   Data           4  my_pid.o(.data)
    Pid_control_mode                         0x20000050   Data           1  my_pid.o(.data)
    pid_params_left                          0x20000054   Data          20  my_pid.o(.data)
    pid_params_right                         0x20000068   Data          20  my_pid.o(.data)
    pid_params_line                          0x2000007c   Data          20  my_pid.o(.data)
    pid_params_angle                         0x20000090   Data          20  my_pid.o(.data)
    pid_running                              0x200000a4   Data           1  my_pid.o(.data)
    Digtal                                   0x200000a8   Data           1  my_gray.o(.data)
    last_last_Digtal                         0x200000a9   Data           1  my_gray.o(.data)
    gray_weights                             0x200000ac   Data          32  my_gray.o(.data)
    g_line_position_error                    0x200000cc   Data           4  my_gray.o(.data)
    Yaw                                      0x200000d0   Data           4  my_hwt101.o(.data)
    hi2c1                                    0x200000e4   Data          84  i2c.o(.bss)
    hi2c2                                    0x20000138   Data          84  i2c.o(.bss)
    htim1                                    0x2000018c   Data          72  tim.o(.bss)
    htim2                                    0x200001d4   Data          72  tim.o(.bss)
    htim3                                    0x2000021c   Data          72  tim.o(.bss)
    htim4                                    0x20000264   Data          72  tim.o(.bss)
    huart4                                   0x200002ac   Data          72  usart.o(.bss)
    huart5                                   0x200002f4   Data          72  usart.o(.bss)
    huart1                                   0x2000033c   Data          72  usart.o(.bss)
    hdma_uart4_rx                            0x20000384   Data          96  usart.o(.bss)
    hdma_uart5_rx                            0x200003e4   Data          96  usart.o(.bss)
    hdma_usart1_rx                           0x20000444   Data          96  usart.o(.bss)
    uart_rx_dma_buffer                       0x200004a4   Data        1024  uart_driver.o(.bss)
    ring_buffer_input                        0x200008a4   Data        1024  uart_driver.o(.bss)
    ring_buffer                              0x20000ca4   Data          12  uart_driver.o(.bss)
    uart_data_buffer                         0x20000cb0   Data        1024  uart_driver.o(.bss)
    uart5_rx_buffer                          0x200010b0   Data        1024  uart_driver.o(.bss)
    uart5_ring_buffer_input                  0x200014b0   Data        1024  uart_driver.o(.bss)
    uart5_ring_buffer                        0x200018b0   Data          12  uart_driver.o(.bss)
    uart5_data_buffer                        0x200018bc   Data        1024  uart_driver.o(.bss)
    right_motor                              0x20001cbc   Data          20  my_motor.o(.bss)
    left_motor                               0x20001cd0   Data          20  my_motor.o(.bss)
    left_encoder                             0x20001ce4   Data          16  my_encoder.o(.bss)
    right_encoder                            0x20001cf4   Data          16  my_encoder.o(.bss)
    pid_speed_left                           0x20001d04   Data          60  my_pid.o(.bss)
    pid_speed_right                          0x20001d40   Data          60  my_pid.o(.bss)
    pid_line                                 0x20001d7c   Data          60  my_pid.o(.bss)
    pid_angle                                0x20001db8   Data          60  my_pid.o(.bss)
    hwt101                                   0x20001df4   Data          68  my_hwt101.o(.bss)
    hwt101_rx_buffer                         0x20001e38   Data          32  my_hwt101.o(.bss)
    __initial_sp                             0x20002258   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000064ec, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00006408, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         4802  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         4849    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         4852    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         4854    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         4856    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         4857    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         4859    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         4861    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         4850    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x0800019c   0x0800019c   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c0   0x080001c0   0x00000062   Code   RO         4805    .text               mc_w.l(uldiv.o)
    0x08000222   0x08000222   0x00000024   Code   RO         4807    .text               mc_w.l(memcpya.o)
    0x08000246   0x08000246   0x00000024   Code   RO         4809    .text               mc_w.l(memseta.o)
    0x0800026a   0x0800026a   0x000000e4   Code   RO         4841    .text               mf_w.l(dmul.o)
    0x0800034e   0x0800034e   0x00000022   Code   RO         4843    .text               mf_w.l(dflti.o)
    0x08000370   0x08000370   0x00000026   Code   RO         4845    .text               mf_w.l(f2d.o)
    0x08000396   0x08000396   0x00000038   Code   RO         4847    .text               mf_w.l(d2f.o)
    0x080003ce   0x080003ce   0x0000002c   Code   RO         4866    .text               mc_w.l(uidiv.o)
    0x080003fa   0x080003fa   0x0000001e   Code   RO         4868    .text               mc_w.l(llshl.o)
    0x08000418   0x08000418   0x00000020   Code   RO         4870    .text               mc_w.l(llushr.o)
    0x08000438   0x08000438   0x00000000   Code   RO         4872    .text               mc_w.l(iusefp.o)
    0x08000438   0x08000438   0x0000006e   Code   RO         4873    .text               mf_w.l(fepilogue.o)
    0x080004a6   0x080004a6   0x000000ba   Code   RO         4875    .text               mf_w.l(depilogue.o)
    0x08000560   0x08000560   0x0000014e   Code   RO         4877    .text               mf_w.l(dadd.o)
    0x080006ae   0x080006ae   0x000000de   Code   RO         4879    .text               mf_w.l(ddiv.o)
    0x0800078c   0x0800078c   0x00000030   Code   RO         4881    .text               mf_w.l(dfixul.o)
    0x080007bc   0x080007bc   0x00000030   Code   RO         4883    .text               mf_w.l(cdrcmple.o)
    0x080007ec   0x080007ec   0x00000024   Code   RO         4885    .text               mc_w.l(init.o)
    0x08000810   0x08000810   0x00000024   Code   RO         4888    .text               mc_w.l(llsshr.o)
    0x08000834   0x08000834   0x00000004   Code   RO          496    i.BusFault_Handler  stm32f4xx_it.o
    0x08000838   0x08000838   0x00000010   Code   RO          497    i.DMA1_Stream0_IRQHandler  stm32f4xx_it.o
    0x08000848   0x08000848   0x00000010   Code   RO          498    i.DMA1_Stream2_IRQHandler  stm32f4xx_it.o
    0x08000858   0x08000858   0x00000010   Code   RO          499    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08000868   0x08000868   0x00000034   Code   RO         1637    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x0800089c   0x0800089c   0x000000aa   Code   RO         1638    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08000946   0x08000946   0x0000002c   Code   RO         1639    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x08000972   0x08000972   0x00000002   Code   RO          500    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000974   0x08000974   0x00000030   Code   RO         4109    i.Encoder_Driver_Init  encoder_driver.o
    0x080009a4   0x080009a4   0x00000068   Code   RO         4110    i.Encoder_Driver_Update  encoder_driver.o
    0x08000a0c   0x08000a0c   0x00000028   Code   RO         4604    i.Encoder_Init      my_encoder.o
    0x08000a34   0x08000a34   0x00000018   Code   RO         4605    i.Encoder_Task      my_encoder.o
    0x08000a4c   0x08000a4c   0x00000006   Code   RO           13    i.Error_Handler     main.o
    0x08000a52   0x08000a52   0x00000002   PAD
    0x08000a54   0x08000a54   0x00000108   Code   RO         4704    i.Gray_Task         my_gray.o
    0x08000b5c   0x08000b5c   0x000000ac   Code   RO         1640    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08000c08   0x08000c08   0x00000028   Code   RO         1641    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08000c30   0x08000c30   0x00000240   Code   RO         1645    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08000e70   0x08000e70   0x000000ec   Code   RO         1646    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08000f5c   0x08000f5c   0x00000092   Code   RO         1650    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08000fee   0x08000fee   0x00000002   PAD
    0x08000ff0   0x08000ff0   0x00000028   Code   RO         2082    i.HAL_Delay         stm32f4xx_hal.o
    0x08001018   0x08001018   0x000001f4   Code   RO         1533    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x0800120c   0x0800120c   0x0000000c   Code   RO         1537    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08001218   0x08001218   0x0000000c   Code   RO         2088    i.HAL_GetTick       stm32f4xx_hal.o
    0x08001224   0x08001224   0x000001d0   Code   RO          649    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x080013f4   0x080013f4   0x00000308   Code   RO          667    i.HAL_I2C_Mem_Read  stm32f4xx_hal_i2c.o
    0x080016fc   0x080016fc   0x00000164   Code   RO          670    i.HAL_I2C_Mem_Write  stm32f4xx_hal_i2c.o
    0x08001860   0x08001860   0x000000f4   Code   RO          300    i.HAL_I2C_MspInit   i2c.o
    0x08001954   0x08001954   0x00000018   Code   RO         2094    i.HAL_IncTick       stm32f4xx_hal.o
    0x0800196c   0x0800196c   0x0000003c   Code   RO         2095    i.HAL_Init          stm32f4xx_hal.o
    0x080019a8   0x080019a8   0x0000004c   Code   RO         2096    i.HAL_InitTick      stm32f4xx_hal.o
    0x080019f4   0x080019f4   0x00000048   Code   RO          614    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08001a3c   0x08001a3c   0x00000028   Code   RO         1925    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08001a64   0x08001a64   0x0000007c   Code   RO         1931    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08001ae0   0x08001ae0   0x00000028   Code   RO         1932    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08001b08   0x08001b08   0x00000184   Code   RO         1134    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08001c8c   0x08001c8c   0x0000000c   Code   RO         1139    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x08001c98   0x08001c98   0x00000020   Code   RO         1141    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08001cb8   0x08001cb8   0x00000020   Code   RO         1142    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08001cd8   0x08001cd8   0x000000b0   Code   RO         1143    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08001d88   0x08001d88   0x0000049c   Code   RO         1146    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08002224   0x08002224   0x00000034   Code   RO         1936    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08002258   0x08002258   0x00000002   Code   RO         3050    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x0800225a   0x0800225a   0x00000002   Code   RO         3051    i.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x0800225c   0x0800225c   0x00000074   Code   RO         3053    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x080022d0   0x080022d0   0x000000b4   Code   RO         3069    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08002384   0x08002384   0x00000066   Code   RO         2335    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x080023ea   0x080023ea   0x00000002   PAD
    0x080023ec   0x080023ec   0x00000074   Code   RO          348    i.HAL_TIM_Base_MspInit  tim.o
    0x08002460   0x08002460   0x000000a8   Code   RO         2340    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08002508   0x08002508   0x0000010c   Code   RO         2344    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08002614   0x08002614   0x000000c8   Code   RO         2356    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x080026dc   0x080026dc   0x000000f8   Code   RO          350    i.HAL_TIM_Encoder_MspInit  tim.o
    0x080027d4   0x080027d4   0x000000cc   Code   RO         2359    i.HAL_TIM_Encoder_Start  stm32f4xx_hal_tim.o
    0x080028a0   0x080028a0   0x00000002   Code   RO         2369    i.HAL_TIM_IC_CaptureCallback  stm32f4xx_hal_tim.o
    0x080028a2   0x080028a2   0x0000016c   Code   RO         2383    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x08002a0e   0x08002a0e   0x00000002   PAD
    0x08002a10   0x08002a10   0x00000064   Code   RO          351    i.HAL_TIM_MspPostInit  tim.o
    0x08002a74   0x08002a74   0x00000002   Code   RO         2386    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x08002a76   0x08002a76   0x00000104   Code   RO         2407    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08002b7a   0x08002b7a   0x00000066   Code   RO         2410    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08002be0   0x08002be0   0x00000002   Code   RO         2412    i.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08002be2   0x08002be2   0x00000002   Code   RO         2413    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08002be4   0x08002be4   0x0000010c   Code   RO         2415    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x08002cf0   0x08002cf0   0x00000040   Code   RO         4421    i.HAL_TIM_PeriodElapsedCallback  scheduler.o
    0x08002d30   0x08002d30   0x00000002   Code   RO         2426    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x08002d32   0x08002d32   0x00000070   Code   RO         3327    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08002da2   0x08002da2   0x00000002   PAD
    0x08002da4   0x08002da4   0x000000c4   Code   RO         3894    i.HAL_UARTEx_RxEventCallback  uart_driver.o
    0x08002e68   0x08002e68   0x0000008a   Code   RO         3341    i.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x08002ef2   0x08002ef2   0x00000002   Code   RO         3343    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08002ef4   0x08002ef4   0x00000308   Code   RO         3346    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080031fc   0x080031fc   0x00000076   Code   RO         3347    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08003272   0x08003272   0x00000002   PAD
    0x08003274   0x08003274   0x000002a4   Code   RO          426    i.HAL_UART_MspInit  usart.o
    0x08003518   0x08003518   0x00000002   Code   RO         3895    i.HAL_UART_RxCpltCallback  uart_driver.o
    0x0800351a   0x0800351a   0x00000002   Code   RO         3354    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x0800351c   0x0800351c   0x000000be   Code   RO         3355    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x080035da   0x080035da   0x00000002   Code   RO         3358    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x080035dc   0x080035dc   0x0000001c   Code   RO         3970    i.HWT101_CalculateChecksum  hwt101_driver.o
    0x080035f8   0x080035f8   0x00000060   Code   RO         3971    i.HWT101_ConvertAngleData  hwt101_driver.o
    0x08003658   0x08003658   0x0000002c   Code   RO         3972    i.HWT101_ConvertGyroData  hwt101_driver.o
    0x08003684   0x08003684   0x00000068   Code   RO         3973    i.HWT101_Create     hwt101_driver.o
    0x080036ec   0x080036ec   0x0000004c   Code   RO         3974    i.HWT101_Enable     hwt101_driver.o
    0x08003738   0x08003738   0x00000028   Code   RO         3975    i.HWT101_GetData    hwt101_driver.o
    0x08003760   0x08003760   0x00000016   Code   RO         3977    i.HWT101_GetState   hwt101_driver.o
    0x08003776   0x08003776   0x0000002a   Code   RO         3979    i.HWT101_ParseAnglePacket  hwt101_driver.o
    0x080037a0   0x080037a0   0x0000002c   Code   RO         3980    i.HWT101_ParseGyroPacket  hwt101_driver.o
    0x080037cc   0x080037cc   0x0000010a   Code   RO         3981    i.HWT101_ProcessBuffer  hwt101_driver.o
    0x080038d6   0x080038d6   0x00000058   Code   RO         3982    i.HWT101_ResetYaw   hwt101_driver.o
    0x0800392e   0x0800392e   0x00000002   PAD
    0x08003930   0x08003930   0x00000038   Code   RO         3983    i.HWT101_SaveConfig  hwt101_driver.o
    0x08003968   0x08003968   0x00000040   Code   RO         3984    i.HWT101_SendCommand  hwt101_driver.o
    0x080039a8   0x080039a8   0x0000006a   Code   RO         3986    i.HWT101_SetOutputRate  hwt101_driver.o
    0x08003a12   0x08003a12   0x00000002   PAD
    0x08003a14   0x08003a14   0x00000028   Code   RO         3989    i.HWT101_UnlockRegister  hwt101_driver.o
    0x08003a3c   0x08003a3c   0x00000012   Code   RO         3990    i.HWT101_ValidateParams  hwt101_driver.o
    0x08003a4e   0x08003a4e   0x00000004   Code   RO          501    i.HardFault_Handler  stm32f4xx_it.o
    0x08003a52   0x08003a52   0x00000002   PAD
    0x08003a54   0x08003a54   0x00000094   Code   RO         4745    i.Hwt101_Init       my_hwt101.o
    0x08003ae8   0x08003ae8   0x000000c4   Code   RO         4746    i.Hwt101_Reinit     my_hwt101.o
    0x08003bac   0x08003bac   0x000000e8   Code   RO         4747    i.Hwt101_RxEventCallback  my_hwt101.o
    0x08003c94   0x08003c94   0x000001e4   Code   RO         4748    i.Hwt101_Task       my_hwt101.o
    0x08003e78   0x08003e78   0x0000003e   Code   RO          693    i.I2C_IsAcknowledgeFailed  stm32f4xx_hal_i2c.o
    0x08003eb6   0x08003eb6   0x00000002   PAD
    0x08003eb8   0x08003eb8   0x00000160   Code   RO          704    i.I2C_RequestMemoryRead  stm32f4xx_hal_i2c.o
    0x08004018   0x08004018   0x000000e0   Code   RO          705    i.I2C_RequestMemoryWrite  stm32f4xx_hal_i2c.o
    0x080040f8   0x080040f8   0x00000066   Code   RO          713    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x0800415e   0x0800415e   0x000000be   Code   RO          714    i.I2C_WaitOnFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x0800421c   0x0800421c   0x000000fa   Code   RO          715    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08004316   0x08004316   0x0000008a   Code   RO          716    i.I2C_WaitOnRXNEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x080043a0   0x080043a0   0x00000066   Code   RO          719    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08004406   0x08004406   0x00000014   Code   RO         3729    i.IIC_Get_Digtal    hardware_iic.o
    0x0800441a   0x0800441a   0x00000002   PAD
    0x0800441c   0x0800441c   0x00000030   Code   RO         3733    i.IIC_ReadBytes     hardware_iic.o
    0x0800444c   0x0800444c   0x00000078   Code   RO          275    i.MX_DMA_Init       dma.o
    0x080044c4   0x080044c4   0x000000f4   Code   RO          251    i.MX_GPIO_Init      gpio.o
    0x080045b8   0x080045b8   0x0000003c   Code   RO          301    i.MX_I2C1_Init      i2c.o
    0x080045f4   0x080045f4   0x0000003c   Code   RO          302    i.MX_I2C2_Init      i2c.o
    0x08004630   0x08004630   0x000000e4   Code   RO          352    i.MX_TIM1_Init      tim.o
    0x08004714   0x08004714   0x0000006c   Code   RO          353    i.MX_TIM2_Init      tim.o
    0x08004780   0x08004780   0x00000080   Code   RO          354    i.MX_TIM3_Init      tim.o
    0x08004800   0x08004800   0x0000007c   Code   RO          355    i.MX_TIM4_Init      tim.o
    0x0800487c   0x0800487c   0x00000038   Code   RO          427    i.MX_UART4_Init     usart.o
    0x080048b4   0x080048b4   0x00000038   Code   RO          428    i.MX_UART5_Init     usart.o
    0x080048ec   0x080048ec   0x00000038   Code   RO          429    i.MX_USART1_UART_Init  usart.o
    0x08004924   0x08004924   0x00000004   Code   RO          502    i.MemManage_Handler  stm32f4xx_it.o
    0x08004928   0x08004928   0x00000096   Code   RO         4158    i.Motor_Create      motor_driver.o
    0x080049be   0x080049be   0x00000002   PAD
    0x080049c0   0x080049c0   0x00000040   Code   RO         4567    i.Motor_Init        my_motor.o
    0x08004a00   0x08004a00   0x00000004   Code   RO          503    i.NMI_Handler       stm32f4xx_it.o
    0x08004a04   0x08004a04   0x00000038   Code   RO         4220    i.OLED_Clear        oled.o
    0x08004a3c   0x08004a3c   0x00000030   Code   RO         4223    i.OLED_Init         oled.o
    0x08004a6c   0x08004a6c   0x00000024   Code   RO         4225    i.OLED_Set_Position  oled.o
    0x08004a90   0x08004a90   0x00000028   Code   RO         4233    i.OLED_Write_cmd    oled.o
    0x08004ab8   0x08004ab8   0x00000028   Code   RO         4234    i.OLED_Write_data   oled.o
    0x08004ae0   0x08004ae0   0x00000110   Code   RO         4652    i.PID_Init          my_pid.o
    0x08004bf0   0x08004bf0   0x00000002   Code   RO          504    i.PendSV_Handler    stm32f4xx_it.o
    0x08004bf2   0x08004bf2   0x00000002   Code   RO          505    i.SVC_Handler       stm32f4xx_it.o
    0x08004bf4   0x08004bf4   0x00000014   Code   RO         4422    i.Scheduler_Init    scheduler.o
    0x08004c08   0x08004c08   0x00000058   Code   RO         4423    i.Scheduler_Run     scheduler.o
    0x08004c60   0x08004c60   0x00000008   Code   RO          506    i.SysTick_Handler   stm32f4xx_it.o
    0x08004c68   0x08004c68   0x000000b4   Code   RO           14    i.SystemClock_Config  main.o
    0x08004d1c   0x08004d1c   0x00000014   Code   RO         3691    i.SystemInit        system_stm32f4xx.o
    0x08004d30   0x08004d30   0x00000028   Code   RO         4424    i.System_Init       scheduler.o
    0x08004d58   0x08004d58   0x00000010   Code   RO          507    i.TIM2_IRQHandler   stm32f4xx_it.o
    0x08004d68   0x08004d68   0x000000e0   Code   RO         2428    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08004e48   0x08004e48   0x00000022   Code   RO         2429    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x08004e6a   0x08004e6a   0x00000016   Code   RO         2439    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x08004e80   0x08004e80   0x00000012   Code   RO         2440    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x08004e92   0x08004e92   0x00000002   PAD
    0x08004e94   0x08004e94   0x00000070   Code   RO         2441    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x08004f04   0x08004f04   0x0000007c   Code   RO         2442    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x08004f80   0x08004f80   0x00000078   Code   RO         2443    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x08004ff8   0x08004ff8   0x00000054   Code   RO         2444    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x0800504c   0x0800504c   0x00000026   Code   RO         2446    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08005072   0x08005072   0x00000028   Code   RO         2448    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x0800509a   0x0800509a   0x00000002   PAD
    0x0800509c   0x0800509c   0x00000010   Code   RO          508    i.UART4_IRQHandler  stm32f4xx_it.o
    0x080050ac   0x080050ac   0x00000010   Code   RO          509    i.UART5_IRQHandler  stm32f4xx_it.o
    0x080050bc   0x080050bc   0x00000012   Code   RO         3360    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x080050ce   0x080050ce   0x00000050   Code   RO         3361    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x0800511e   0x0800511e   0x000000b4   Code   RO         3362    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x080051d2   0x080051d2   0x00000024   Code   RO         3364    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x080051f6   0x080051f6   0x0000006c   Code   RO         3370    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08005262   0x08005262   0x00000020   Code   RO         3371    i.UART_EndTransmit_IT  stm32f4xx_hal_uart.o
    0x08005282   0x08005282   0x00000026   Code   RO         3372    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x080052a8   0x080052a8   0x000000fc   Code   RO         3373    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x080053a4   0x080053a4   0x0000022c   Code   RO         3374    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x080055d0   0x080055d0   0x000000d8   Code   RO         3375    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x080056a8   0x080056a8   0x00000060   Code   RO         3377    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x08005708   0x08005708   0x0000008c   Code   RO         3378    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08005794   0x08005794   0x00000010   Code   RO          510    i.USART1_IRQHandler  stm32f4xx_it.o
    0x080057a4   0x080057a4   0x00000044   Code   RO         4533    i.Uart_Init         my_usart.o
    0x080057e8   0x080057e8   0x00000040   Code   RO         3896    i.Uart_Passthrough_Init  uart_driver.o
    0x08005828   0x08005828   0x0000003a   Code   RO         3898    i.Uart_Printf       uart_driver.o
    0x08005862   0x08005862   0x00000004   Code   RO          511    i.UsageFault_Handler  stm32f4xx_it.o
    0x08005866   0x08005866   0x00000002   PAD
    0x08005868   0x08005868   0x0000002c   Code   RO         4819    i.__0vsnprintf      mc_w.l(printfa.o)
    0x08005894   0x08005894   0x00000010   Code   RO         1938    i.__NVIC_GetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x080058a4   0x080058a4   0x00000028   Code   RO         1939    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080058cc   0x080058cc   0x0000000e   Code   RO         4893    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080058da   0x080058da   0x00000002   Code   RO         4894    i.__scatterload_null  mc_w.l(handlers.o)
    0x080058dc   0x080058dc   0x0000000e   Code   RO         4895    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080058ea   0x080058ea   0x00000002   PAD
    0x080058ec   0x080058ec   0x00000184   Code   RO         4821    i._fp_digits        mc_w.l(printfa.o)
    0x08005a70   0x08005a70   0x000006dc   Code   RO         4822    i._printf_core      mc_w.l(printfa.o)
    0x0800614c   0x0800614c   0x00000024   Code   RO         4823    i._printf_post_padding  mc_w.l(printfa.o)
    0x08006170   0x08006170   0x0000002e   Code   RO         4824    i._printf_pre_padding  mc_w.l(printfa.o)
    0x0800619e   0x0800619e   0x00000016   Code   RO         4825    i._snputc           mc_w.l(printfa.o)
    0x080061b4   0x080061b4   0x00000040   Code   RO           15    i.main              main.o
    0x080061f4   0x080061f4   0x0000003a   Code   RO          430    i.my_printf         usart.o
    0x0800622e   0x0800622e   0x00000002   PAD
    0x08006230   0x08006230   0x00000054   Code   RO         4345    i.pid_init          pid.o
    0x08006284   0x08006284   0x00000006   Code   RO         4350    i.pid_set_target    pid.o
    0x0800628a   0x0800628a   0x0000003c   Code   RO         3808    i.rt_ringbuffer_data_len  ringbuffer.o
    0x080062c6   0x080062c6   0x00000030   Code   RO         3811    i.rt_ringbuffer_init  ringbuffer.o
    0x080062f6   0x080062f6   0x000000a8   Code   RO         3813    i.rt_ringbuffer_put  ringbuffer.o
    0x0800639e   0x0800639e   0x0000002a   Code   RO         3818    i.rt_ringbuffer_status  ringbuffer.o
    0x080063c8   0x080063c8   0x00000008   Data   RO         1652    .constdata          stm32f4xx_hal_dma.o
    0x080063d0   0x080063d0   0x00000018   Data   RO         3692    .constdata          system_stm32f4xx.o
    0x080063e8   0x080063e8   0x00000020   Data   RO         4891    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08006408, Size: 0x00002258, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08006408   0x00000009   Data   RW         2102    .data               stm32f4xx_hal.o
    0x20000009   0x08006411   0x00000003   PAD
    0x2000000c   0x08006414   0x00000004   Data   RW         3693    .data               system_stm32f4xx.o
    0x20000010   0x08006418   0x00000016   Data   RW         4236    .data               oled.o
    0x20000026   0x0800642e   0x00000002   PAD
    0x20000028   0x08006430   0x00000024   Data   RW         4425    .data               scheduler.o
    0x2000004c   0x08006454   0x00000059   Data   RW         4655    .data               my_pid.o
    0x200000a5   0x080064ad   0x00000003   PAD
    0x200000a8   0x080064b0   0x00000028   Data   RW         4705    .data               my_gray.o
    0x200000d0   0x080064d8   0x00000014   Data   RW         4751    .data               my_hwt101.o
    0x200000e4        -       0x000000a8   Zero   RW          303    .bss                i2c.o
    0x2000018c        -       0x00000120   Zero   RW          356    .bss                tim.o
    0x200002ac        -       0x000001f8   Zero   RW          431    .bss                usart.o
    0x200004a4        -       0x00001818   Zero   RW         3899    .bss                uart_driver.o
    0x20001cbc        -       0x00000028   Zero   RW         4568    .bss                my_motor.o
    0x20001ce4        -       0x00000020   Zero   RW         4606    .bss                my_encoder.o
    0x20001d04        -       0x000000f0   Zero   RW         4654    .bss                my_pid.o
    0x20001df4        -       0x00000064   Zero   RW         4749    .bss                my_hwt101.o
    0x20001e58        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x080064ec, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       120          6          0          0          0        798   dma.o
       152         20          0          0          0       2263   encoder_driver.o
       244         10          0          0          0       1031   gpio.o
        68          6          0          0          0       1321   hardware_iic.o
      1134         56          0          0          0      12719   hwt101_driver.o
       364         40          0          0        168       2258   i2c.o
       250         10          0          0          0     703088   main.o
       150          0          0          0          0       1947   motor_driver.o
        64         24          0          0         32       1783   my_encoder.o
       264         60          0         40          0       1393   my_gray.o
      1060        520          0         20        100       3207   my_hwt101.o
        64         18          0          0         40        706   my_motor.o
       272         40          0         89        240       1510   my_pid.o
        68         20          0          0          0        472   my_usart.o
       220         18          0         22          0       3694   oled.o
        90          6          0          0          0       2072   pid.o
       318          0          0          0          0       5290   ringbuffer.o
       212         32          0         36          0       3176   scheduler.o
        36          8        392          0       1024        820   startup_stm32f407xx.o
       212         36          0          9          0       9309   stm32f4xx_hal.o
       312         22          0          0          0      34259   stm32f4xx_hal_cortex.o
      1436         16          8          0          0       6930   stm32f4xx_hal_dma.o
       512         46          0          0          0       2080   stm32f4xx_hal_gpio.o
      3016         48          0          0          0      11738   stm32f4xx_hal_i2c.o
        72          4          0          0          0        838   stm32f4xx_hal_msp.o
      1820         84          0          0          0       5556   stm32f4xx_hal_rcc.o
      2762        142          0          0          0      18331   stm32f4xx_hal_tim.o
       300         30          0          0          0       3301   stm32f4xx_hal_tim_ex.o
      3092         28          0          0          0      16326   stm32f4xx_hal_uart.o
       146         42          0          0          0       7556   stm32f4xx_it.o
        20          6         24          4          0       1099   system_stm32f4xx.o
      1052         76          0          0        288       5277   tim.o
       320         64          0          0       6168       5812   uart_driver.o
       902         80          0          0        504       5963   usart.o

    ----------------------------------------------------------------------
     21156       <USER>        <GROUP>        228       8564     883923   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        32          0          0          8          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2292         84          0          0          0        516   printfa.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      3996        <USER>          <GROUP>          0          0       2192   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2690        100          0          0          0       1136   mc_w.l
      1304          0          0          0          0       1056   mf_w.l

    ----------------------------------------------------------------------
      3996        <USER>          <GROUP>          0          0       2192   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     25152       1718        456        228       8564     868927   Grand Totals
     25152       1718        456        228       8564     868927   ELF Image Totals
     25152       1718        456        228          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                25608 (  25.01kB)
    Total RW  Size (RW Data + ZI Data)              8792 (   8.59kB)
    Total ROM Size (Code + RO Data + RW Data)      25836 (  25.23kB)

==============================================================================

