/**
 ******************************************************************************
 * @file    My_hwt101.c
 * @brief   HWT101陀螺仪应用层实现 - 精简版
 ******************************************************************************
 */

#include "My_hwt101.h"
#include <string.h>

/* External variables --------------------------------------------------------*/
extern DMA_HandleTypeDef hdma_uart4_rx;

/* Private variables ---------------------------------------------------------*/
HWT101_t hwt101;
uint8_t hwt101_rx_buffer[HWT101_BUFFER_SIZE];

float Yaw;  // 全局偏航角变量，供外部模块使用

// 错误计数器和统计
static uint32_t error_count = 0;
static uint32_t last_valid_time = 0;
static uint32_t rx_packet_count = 0;
static uint32_t last_rx_time = 0;

// 函数前向声明
static void Hwt101_Reinit(void);

/**
 * @brief HWT101初始化函数
 */
void Hwt101_Init(void)
{
    // 先创建HWT101驱动实例
    HWT101_Create(&hwt101, &huart4, 1000);

    // 启动串口4的DMA接收
    HAL_UARTEx_ReceiveToIdle_DMA(&huart4, hwt101_rx_buffer, HWT101_BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_uart4_rx, DMA_IT_HT);

    // 使能HWT101驱动
    HWT101_Enable(&hwt101, 1);

    // 等待传感器稳定并设置输出频率
    HAL_Delay(1000);
    HWT101_SetOutputRate(&hwt101, 6);

    // 以开机方向为0°（硬件归零）
    HAL_Delay(500);  // 等待输出频率设置生效
    HWT101_ResetYaw(&hwt101);

    // 调试：检查初始化状态
    Uart_Printf(&huart1, "[HWT101] Init complete, DMA started\r\n");
}

/**
 * @brief HWT101任务处理函数
 */
void Hwt101_Task(void)
{
    // 获取HWT101数据和状态
    HWT101_Data_t* data = HWT101_GetData(&hwt101);
    HWT101_State_t state = HWT101_GetState(&hwt101);
    uint32_t current_time = HAL_GetTick();

    if (data != NULL && data->data_valid) {
        Yaw = data->yaw;
        Uart_Printf(&huart1,"{Yaw}%.2f\n", Yaw);
				Uart_Printf(&huart1,"{ff_count}%d\n", ff_count);
        error_count = 0;  // 重置错误计数
        last_valid_time = current_time;  // 更新最后有效时间
    } else {
        error_count++;

        // 数据无效时输出调试信息
        Uart_Printf(&huart1,"{Yaw}Invalid - State:%d, Valid:%d, Errors:%d [T:%d]\n",
                   state, data ? data->data_valid : 0, error_count, current_time);

        // 检查是否还在接收数据包
        if (current_time - last_rx_time > 2000) {
            Uart_Printf(&huart1, "[HWT101] No RX packets for 2s, last RX: %d\r\n", last_rx_time);
        }

        // 如果连续错误超过50次或超过5秒没有有效数据，尝试重新初始化
        if (error_count > 50 || (current_time - last_valid_time > 5000)) {
            Uart_Printf(&huart1, "[HWT101] Too many errors, reinitializing...\r\n");
            Uart_Printf(&huart1, "[HWT101] RX packets: %d, Last RX: %d ms ago\r\n",
                       rx_packet_count, current_time - last_rx_time);
            Hwt101_Reinit();
            error_count = 0;
            last_valid_time = current_time;
        }
    }
}

/**
 * @brief 串口4接收完成回调函数
 * @note 这个函数在uart_driver.c的HAL_UARTEx_RxEventCallback中被调用
 */
void Hwt101_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    if (huart->Instance == UART4)
    {
        rx_packet_count++;
        last_rx_time = HAL_GetTick();

        #ifdef DEBUG_HWT101_RX
        // 调试：打印接收到的原始数据（减少输出频率）
        if (rx_packet_count % 10 == 0) {  // 每10个包输出一次
            Uart_Printf(&huart1, "RX[%d]#%d: ", Size, rx_packet_count);
            for(uint16_t i = 0; i < Size && i < 11; i++) {
                Uart_Printf(&huart1, "%02X ", hwt101_rx_buffer[i]);
            }
            Uart_Printf(&huart1, "\r\n");
        }
        #endif

        // 检查接收数据大小是否合理
        if (Size > 0 && Size <= HWT101_BUFFER_SIZE) {
            // 处理接收到的数据
            int8_t result = HWT101_ProcessBuffer(&hwt101, hwt101_rx_buffer, Size);

            // 检查处理结果
            if (result != 0) {
                Uart_Printf(&huart1, "[HWT101] ProcessBuffer failed: %d, Size: %d\r\n", result, Size);

                #ifdef DEBUG_HWT101_RX
                // 失败时打印更详细的信息
                Uart_Printf(&huart1, "Failed data: ");
                for(uint16_t i = 0; i < Size && i < 11; i++) {
                    Uart_Printf(&huart1, "%02X ", hwt101_rx_buffer[i]);
                }
                Uart_Printf(&huart1, "\r\n");
                #endif
            }
        } else {
            Uart_Printf(&huart1, "[HWT101] Invalid data size: %d\r\n", Size);
        }

        // 清空接收缓冲区
        memset(hwt101_rx_buffer, 0, Size);

        // 重新启动DMA接收
        HAL_UARTEx_ReceiveToIdle_DMA(&huart4, hwt101_rx_buffer, HWT101_BUFFER_SIZE);
        __HAL_DMA_DISABLE_IT(&hdma_uart4_rx, DMA_IT_HT);
    }
}

/**
 * @brief 获取当前偏航角
 * @retval 当前偏航角值(度)
 */
float Hwt101_GetCurrentYaw(void)
{
    return Yaw;
}

/**
 * @brief HWT101调试数据输出
 * @note 用于调试和监控HWT101状态
 */
void Hwt101_DebugData(void)
{
    HWT101_Data_t* data = HWT101_GetData(&hwt101);
    HWT101_State_t state = HWT101_GetState(&hwt101);

    if (data != NULL) {
        Uart_Printf(&huart1, "[HWT101] Yaw=%.2f°, GyroZ=%.2f°/s, Valid=%d, State=%d, Enable=%d\r\n",
                   data->yaw, data->gyro_z, data->data_valid, state, hwt101.enable);
    } else {
        Uart_Printf(&huart1, "[HWT101] No data available, State=%d, Enable=%d\r\n", state, hwt101.enable);
    }
}

/**
 * @brief 重新初始化HWT101
 * @note 当检测到通信异常时调用此函数尝试恢复
 */
static void Hwt101_Reinit(void)
{
    Uart_Printf(&huart1, "[HWT101] Reinitializing...\r\n");

    // 停止DMA接收
    HAL_UART_DMAStop(&huart4);
    HAL_Delay(100);

    // 清空接收缓冲区
    memset(hwt101_rx_buffer, 0, HWT101_BUFFER_SIZE);

    // 重新创建和使能驱动
    HWT101_Create(&hwt101, &huart4, 1000);
    HWT101_Enable(&hwt101, 1);

    // 重新启动DMA接收
    HAL_UARTEx_ReceiveToIdle_DMA(&huart4, hwt101_rx_buffer, HWT101_BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_uart4_rx, DMA_IT_HT);

    // 等待稳定并设置输出频率
    HAL_Delay(500);
    HWT101_SetOutputRate(&hwt101, 6);

    // 重新归零
    HAL_Delay(200);
    HWT101_ResetYaw(&hwt101);

    Uart_Printf(&huart1, "[HWT101] Reinit complete\r\n");
}


