#include "My_pid.h"

extern Encoder left_encoder;
extern Encoder right_encoder;

extern Motor_t left_motor;
extern Motor_t right_motor;

int Basic_Speed = 30;

/* PID 控制器实例 */
PID_T pid_speed_left;  // 左轮速度环
PID_T pid_speed_right; // 右轮速度环

PID_T pid_line; // 循迹环

PID_T pid_angle; // 角度环

unsigned char Pid_control_mode = 1;

// 增量式PID：P-稳定性，I-响应性，D-准确性
PidParams_t pid_params_left = {
    .kp = 1.40f,
    .ki = 0.04f,
    .kd = 0.03f,
    .out_min = -999.0f,
    .out_max = 999.0f,
};
     
PidParams_t pid_params_right = {
		.kp = 1.8f,
    .ki = 0.3f,
    .kd = 0.02f, 
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_line = {
		.kp = 0.6f,     
    .ki = 0.2f,      
    .kd = 0.0f,      
    .out_min = -50.0f,   
    .out_max = 50.0f,
};


PidParams_t pid_params_angle = {
		.kp = 0.7f,     
    .ki = 0.0f,      
    .kd = 0.002f,      
    .out_min = -50.0f,   
    .out_max = 50.0f,
};

void PID_Init(void)
{
  pid_init(&pid_speed_left,
           pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,
           0.0f, pid_params_left.out_max);
  
  pid_init(&pid_speed_right,
           pid_params_right.kp, pid_params_right.ki, pid_params_right.kd,
           0.0f, pid_params_right.out_max);
  pid_init(&pid_line,
           pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
           0.0f, pid_params_line.out_max);
  pid_init(&pid_angle,
           pid_params_angle.kp, pid_params_angle.ki, pid_params_angle.kd,
           0.0f, pid_params_angle.out_max);

  // 设置速度环目标值
  pid_set_target(&pid_speed_left, Basic_Speed);
  pid_set_target(&pid_speed_right, Basic_Speed);
  pid_set_target(&pid_line, 0);
  pid_set_target(&pid_angle, 0);
}

bool pid_running = true; // PID 控制使能开关

void Line_PID_control(void) // 循迹环控制
{
  int line_pid_output = 0;

  line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);

  // 输出限幅
  line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);

  // 将差值作用在速度环的目标量上
  pid_set_target(&pid_speed_left, Basic_Speed - line_pid_output);
  pid_set_target(&pid_speed_right, Basic_Speed + line_pid_output);
}

void Angle_PID_control(void) // 角度环控制
{
  int angle_pid_output = 0;

  angle_pid_output = pid_calculate_positional(&pid_angle,Yaw);

  // 输出限幅
  angle_pid_output = pid_constrain(angle_pid_output, pid_params_angle.out_min, pid_params_angle.out_max);

  // 将差值作用在速度环的目标量上
  pid_set_target(&pid_speed_left, Basic_Speed - angle_pid_output);
  pid_set_target(&pid_speed_right, Basic_Speed + angle_pid_output);
}

void PID_Task(void)
{
    if(pid_running == false) return;

    int output_left = 0, output_right = 0;
    
//    if(Pid_control_mode == 0)
//    Angle_PID_control();
//    else
    Line_PID_control();

  //位置式PID计算输出值
    output_left = pid_calculate_incremental(&pid_speed_left, left_encoder.speed_cm_s);
    output_right = pid_calculate_incremental(&pid_speed_right, right_encoder.speed_cm_s);

  //输出限幅
    output_left = pid_constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
    output_right = pid_constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);

    // 设置电机速度 - 修复：左电机用左输出，右电机用右输出
    Motor_SetSpeed(&left_motor, output_left);
    Motor_SetSpeed(&right_motor, output_right);

    
}

