#include "My_oled.h"

// ???? OLED ????? 128 ???????? 6x8 ????
// ??? 8 ????????? 64/8 = 8 ?? (y=0~7) ?? 32/8 = 4 ?? (y=0~3)
// ??? 6 ?????????? 128/6 = 21 ????? (x=0~20? ????????????????��??)
// **???:** Oled_Printf ?? x, y ??????��????��? OLED_ShowStr ?????????????��???????��??
// ????��???? (0-127, 0-3) ????????? 128x32 ????????? x ?????????? y ????

/**
 * @brief	???????printf?????????????????6x8??��??ASCII???
 * @param x  ??? X ???? (????) ?? ?????��?? (????? OLED_ShowStr)
 * @param y  ??? Y ???? (????) ?? ?????��?? (????? OLED_ShowStr, 0-3 ?? 0-7)
 * @param format, ... ????????????????
 * ???��Oled_Printf(0, 0, "Data = %d", dat);
**/
int Oled_Printf(uint8_t x, uint8_t y, const char *format, ...)
{
	char buffer[512]; // ????????��???????????
	va_list arg;
	int len;

	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);

	// ???? OLED_ShowStr ??????????? x ??????? y
	OLED_ShowStr(x, y, (uint8_t*)buffer, 8); // ?? buffer ?? uint8_t*
	return len;
}

/**
 * @brief ?? WouoUI ???????????????? OLED ???
 * @param buff WouoUI ??????????????��? [???/8][????] ?? [8][128] for 128x64
 */
void OLED_SendBuff(uint8_t buff[8][128])
{  
    // ??????? (0-7)??0.96?�T??8?
    for(uint8_t page = 0; page < 8; page++)  
    {  
        // ???? OLED ??????
        OLED_Write_cmd(0xb0 + page); // 0xB0 - 0xB7
        
        // ???? OLED ???��?? (??? 0 ?��??)
        OLED_Write_cmd(0x00); // ???????��??
        OLED_Write_cmd(0x10); // ???????��?? (0x10 | 0 = 0)

        // ???��?????? 128 ??????
        for (uint8_t column = 0; column < 128; column++)  
        {
            // ?? WouoUI ???????????????????????????????��????????
            OLED_Write_data(buff[page][column]); 
        }
    } 
}
	float Num =0;
/* Oled ??????? */
void oled_task(void)
{

}

