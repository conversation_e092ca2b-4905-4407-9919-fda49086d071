#include "scheduler.h"
#include "My_hwt101.h"  // 添加HWT101支持

// 任务结构体
typedef struct {
  void (*task_func)(void);  // 任务函数指针
  uint32_t rate_ms;         // 执行周期（毫秒）
  uint32_t last_run;        // 上次执行时间（初始化为 0，每次运行时刷新）
} scheduler_task_t;

// 全局变量，用于存储任务数量
uint8_t task_num;

/**
 * @brief 用户初始化函数
 * 非HAL库硬件初始化函数
 */
void System_Init()
{
	OLED_Init();
  Uart_Init();
  Motor_Init();
  Encoder_Init();
  Hwt101_Init();
  PID_Init();
	HAL_TIM_Base_Start_IT(&htim2);
}

// 静态任务数组，每个任务包含任务函数、执行周期（毫秒）和上次运行时间（毫秒）
static scheduler_task_t scheduler_task[] =
{
  {Hwt101_Task,10,0},
};


/**
 * @brief 调度器初始化函数
 * 计算任务数组的元素个数，并将结果存储在 task_num 中
 */
void Scheduler_Init(void)
{
  System_Init();
  // 计算任务数组的元素个数，并将结果存储在 task_num 中
  task_num = sizeof(scheduler_task) / sizeof(scheduler_task_t); // 数组大小 / 数组成员大小 = 数组元素个数
}

/**
 * @brief 调度器运行函数
 * 遍历任务数组，检查是否有任务需要执行。如果当前时间已经超过任务的执行周期，则执行该任务并更新上次运行时间
 */
void Scheduler_Run(void)
{
  // 遍历任务数组中的所有任务
  for (uint8_t i = 0; i < task_num; i++)
  {
    // 获取当前的系统时间（毫秒）
    uint32_t now_time = HAL_GetTick();

    // 检查当前时间是否达到任务的执行时间
    if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
    {
      // 更新任务的上次运行时间为当前时间
      scheduler_task[i].last_run = now_time;

      // 执行任务函数
      scheduler_task[i].task_func();
    }
  }
}



unsigned char Car_State = 1;  //0 为初始化 1为空白  2为线上
unsigned char Last_Car_Statr = 1;
unsigned int Line_State_Time = 0;	//记录在状态上的时间，在一个状态上保持一段时间才判定为是这个状态
unsigned int White_State_Time = 0;	//记录在状态上的时间，在一个状态上保持一段时间才判定为是这个状态
unsigned int ff_count = 0;

extern PID_T pid_line;
extern bool pid_running;

/**
 * @brief TIM2定时器中断回调函数 - 每1ms执行一次
 * @param htim: 定时器句柄指针
 * @retval None
 */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
    if(htim->Instance != htim2.Instance)return;
    if (htim->Instance == TIM2)
    {
      static uint32_t task_counter_5ms = 0;

      if(++task_counter_5ms >= 5)
      {
        task_counter_5ms = 0;
        Encoder_Task();
        Gray_Task();
//        PID_Task();       
		  }
    }

//		if(Digtal == 0x00)
//		{
//      Line_State_Time = 0;
//			if(++White_State_Time == 800)
//			{
//				White_State_Time = 800;
//			}
//			if(White_State_Time == 800)
//			{
//				Car_State = 1;
//			}
//		}else
//		{
//      White_State_Time = 0;
//			if(++Line_State_Time == 800)
//      {
//        Line_State_Time = 800;
//      }
//      if(Line_State_Time == 800)
//      {
//        Car_State = 2;
//      }
//		}

//    if(Last_Car_Statr != Car_State)
//    {
//      Last_Car_Statr = Car_State;
//      ff_count++;
//      Car_State_Upstate();
//    }
		
//  /* 出圈触发器 */
//  if(Digtal != 0x00)
//  {
//    output_flag = 1;
//    output_timer500ms = 0;
//    if(++intput_timer500ms >= 800) intput_timer500ms = 800;
//  }
//  else if(output_flag == 1 && intput_timer500ms == 800)
//  {
//    output_flag = 0;
//    intput_timer500ms = 0;
//    ff_count++;
//    Car_State_Upstate();
//  }
//  
//  /* 入圈触发器 */
//  if(Digtal == 0x00)
//  {
//    intput_flag = 1;
//		intput_timer500ms = 0;
//    if(++output_timer500ms >= 800) output_timer500ms = 800;
//  }
//  else if(intput_flag == 1 && output_timer500ms == 800)
//  {
//    intput_flag = 0;
//    output_timer500ms = 0;
//    ff_count++;
//    Car_State_Upstate();
//  }
}

void Car_State_Upstate(void)
{
      if(ff_count == 1)
      {
        Pid_control_mode = 1; // 使用循迹环控制
      }
      else if(ff_count == 2)
      {
        Pid_control_mode = 0; // 使用角度环控制
        pid_set_target(&pid_angle, -155);
      }
      else if(ff_count == 3)
      {
        Pid_control_mode = 1; // 使用循迹环控制
      }
      else if(ff_count == 4)
      {
        pid_running = 0;
        Motor_Stop(&left_motor);
        Motor_Stop(&right_motor);
      }
      
      pid_reset(&pid_line);
      pid_reset(&pid_angle);
}



