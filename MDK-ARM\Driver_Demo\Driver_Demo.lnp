--cpu=Cortex-M4.fp.sp
"driver_demo\startup_stm32f407xx.o"
"driver_demo\main.o"
"driver_demo\gpio.o"
"driver_demo\dma.o"
"driver_demo\i2c.o"
"driver_demo\tim.o"
"driver_demo\usart.o"
"driver_demo\stm32f4xx_it.o"
"driver_demo\stm32f4xx_hal_msp.o"
"driver_demo\stm32f4xx_hal_i2c.o"
"driver_demo\stm32f4xx_hal_i2c_ex.o"
"driver_demo\stm32f4xx_hal_rcc.o"
"driver_demo\stm32f4xx_hal_rcc_ex.o"
"driver_demo\stm32f4xx_hal_flash.o"
"driver_demo\stm32f4xx_hal_flash_ex.o"
"driver_demo\stm32f4xx_hal_flash_ramfunc.o"
"driver_demo\stm32f4xx_hal_gpio.o"
"driver_demo\stm32f4xx_hal_dma_ex.o"
"driver_demo\stm32f4xx_hal_dma.o"
"driver_demo\stm32f4xx_hal_pwr.o"
"driver_demo\stm32f4xx_hal_pwr_ex.o"
"driver_demo\stm32f4xx_hal_cortex.o"
"driver_demo\stm32f4xx_hal.o"
"driver_demo\stm32f4xx_hal_exti.o"
"driver_demo\stm32f4xx_hal_tim.o"
"driver_demo\stm32f4xx_hal_tim_ex.o"
"driver_demo\stm32f4xx_hal_uart.o"
"driver_demo\system_stm32f4xx.o"
"driver_demo\hardware_iic.o"
"driver_demo\ringbuffer.o"
"driver_demo\uart_driver.o"
"driver_demo\hwt101_driver.o"
"driver_demo\encoder_driver.o"
"driver_demo\motor_driver.o"
"driver_demo\oled.o"
"driver_demo\pid.o"
"driver_demo\scheduler.o"
"driver_demo\my_oled.o"
"driver_demo\my_usart.o"
"driver_demo\my_motor.o"
"driver_demo\my_encoder.o"
"driver_demo\my_pid.o"
"driver_demo\my_gray.o"
"driver_demo\my_hwt101.o"
--library_type=microlib --strict --scatter "Driver_Demo\Driver_Demo.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "Driver_Demo.map" -o Driver_Demo\Driver_Demo.axf