#ifndef __MY_PID_H__
#define __MY_PID_H__

#include "MyDefine.h"

// PID参数结构体
typedef struct
{
    float kp;          // 比例系数
    float ki;          // 积分系数
    float kd;          // 微分系数
    float out_min;     // 输出最小值
    float out_max;     // 输出最大值
} PidParams_t;

void PID_Init(void);
void PID_Task(void);

void Line_PID_control(void); // 循迹环控制

extern unsigned char Pid_control_mode;
extern PID_T pid_angle; // 角度环

#endif
