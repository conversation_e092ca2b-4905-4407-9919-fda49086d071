#include "My_encoder.h"

// 左右编码器对象
Encoder left_encoder;
Encoder right_encoder;

/**
 * @brief 初始化编码器应用
 */
void Encoder_Init(void)
{
  Encoder_Driver_Init(&left_encoder, &htim4, 0);
  Encoder_Driver_Init(&right_encoder, &htim3,1);
}

/**
 * @brief 编码器应用运行任务 (应由调度器周期性调用)
 */
void Encoder_Task(void)
{
  Encoder_Driver_Update(&left_encoder);
  Encoder_Driver_Update(&right_encoder);

	// // 输出详细的调试信息
	// Uart_Printf(&huart5,"[DEBUG] Left - Count:%d, Speed:%.2f cm/s\n",
	//            left_encoder.count, left_encoder.speed_cm_s);

	// // 输出原始定时器计数值用于硬件调试
	// uint32_t raw_timer_count = __HAL_TIM_GetCounter(&htim4);
	// Uart_Printf(&huart5,"[DEBUG] Raw Timer Count: %lu\n", raw_timer_count);

//	 Uart_Printf(&huart1,"{left}%.2f\n",left_encoder.speed_cm_s);
//	 Uart_Printf(&huart1,"{right}%.2f\n",right_encoder.speed_cm_s);
}
