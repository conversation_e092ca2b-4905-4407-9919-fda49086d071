#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "stdint.h"
#include "stdlib.h"

/* ========== HAL 库头文件 ========== */
#include "main.h"
#include "gpio.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"

/* ========== C 标准头文件 ========== */
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <math.h>
#include <stdint.h>
#include <stdbool.h>

/* ========== 自定义驱动头文件 ========== */
#include "Scheduler.h"
//oled
#include "i2c.h"
#include "oled.h"
#include "My_oled.h"

//串口
#include "My_usart.h"
#include "uart_driver.h"
#include "ringbuffer.h"

//电机
#include "My_motor.h"
#include "motor_driver.h"
#include "My_encoder.h"
#include "encoder_driver.h"

//PID
#include "My_pid.h"
#include "pid.h"

//感为循迹
#include "My_gray.h"

//陀螺仪HWT101
#include "hwt101_driver.h"
#include "My_hwt101.h"


//电机对象
extern Motor_t right_motor;
extern Motor_t left_motor;


extern DMA_HandleTypeDef hdma_usart1_rx;

